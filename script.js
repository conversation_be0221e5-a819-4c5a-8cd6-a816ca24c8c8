// Tracking system for Discord webhook
const DISCORD_WEBHOOK_URL = 'https://discord.com/api/webhooks/1306946105636028417/1ewbscCHtbYG-pCi1KGNTl2UwLRYo0AE7PeZYt7TeIV6MrXISzAGCqhgl_DKuGBsUpvv';

// Function to collect device information
function collectDeviceInfo() {
  const info = {
    userAgent: navigator.userAgent,
    platform: navigator.platform,
    language: navigator.language,
    cookieEnabled: navigator.cookieEnabled,
    onLine: navigator.onLine,
    screenWidth: screen.width,
    screenHeight: screen.height,
    colorDepth: screen.colorDepth,
    pixelDepth: screen.pixelDepth,
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    timestamp: new Date().toISOString(),
    url: window.location.href,
    referrer: document.referrer || 'Direct',
    viewportWidth: window.innerWidth,
    viewportHeight: window.innerHeight
  };

  // Try to get more detailed browser info
  try {
    info.browser = {
      name: getBrowserName(),
      version: getBrowserVersion(),
      engine: getBrowserEngine()
    };
  } catch (e) {
    info.browser = 'Unknown';
  }

  return info;
}

// Helper functions to detect browser information
function getBrowserName() {
  const userAgent = navigator.userAgent;
  if (userAgent.includes('Chrome')) return 'Chrome';
  if (userAgent.includes('Firefox')) return 'Firefox';
  if (userAgent.includes('Safari')) return 'Safari';
  if (userAgent.includes('Edge')) return 'Edge';
  if (userAgent.includes('Opera')) return 'Opera';
  return 'Unknown';
}

function getBrowserVersion() {
  const userAgent = navigator.userAgent;
  const match = userAgent.match(/(chrome|firefox|safari|edge|opera)\/?\s*(\d+)/i);
  return match ? match[2] : 'Unknown';
}

function getBrowserEngine() {
  const userAgent = navigator.userAgent;
  if (userAgent.includes('Gecko')) return 'Gecko';
  if (userAgent.includes('WebKit')) return 'WebKit';
  if (userAgent.includes('Blink')) return 'Blink';
  if (userAgent.includes('Trident')) return 'Trident';
  return 'Unknown';
}

// Function to send tracking data to Discord
async function sendTrackingData(action, additionalData = {}) {
  try {
    const deviceInfo = collectDeviceInfo();
    const trackingData = {
      ...deviceInfo,
      action: action,
      ...additionalData
    };

    const embed = {
      title: '🌐 Website Visitor Tracking',
      color: 0x00ff00,
      fields: [
        {
          name: '📱 Action',
          value: action,
          inline: true
        },
        {
          name: '🌍 Platform',
          value: deviceInfo.platform,
          inline: true
        },
        {
          name: '🌐 Browser',
          value: `${deviceInfo.browser.name} ${deviceInfo.browser.version}`,
          inline: true
        },
        {
          name: '📏 Screen Resolution',
          value: `${deviceInfo.screenWidth}x${deviceInfo.screenHeight}`,
          inline: true
        },
        {
          name: '🖥️ Viewport',
          value: `${deviceInfo.viewportWidth}x${deviceInfo.viewportHeight}`,
          inline: true
        },
        {
          name: '🌍 Timezone',
          value: deviceInfo.timezone,
          inline: true
        },
        {
          name: '🔗 URL',
          value: deviceInfo.url,
          inline: false
        },
        {
          name: '📄 Referrer',
          value: deviceInfo.referrer,
          inline: false
        },
        {
          name: '⏰ Timestamp',
          value: deviceInfo.timestamp,
          inline: false
        }
      ],
      footer: {
        text: 'Tactical Battle Game Tracking'
      }
    };

    // Add additional data if provided
    if (Object.keys(additionalData).length > 0) {
      embed.fields.push({
        name: '📊 Additional Data',
        value: JSON.stringify(additionalData, null, 2),
        inline: false
      });
    }

    const payload = {
      embeds: [embed]
    };

    await fetch(DISCORD_WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload)
    });

    console.log('Tracking data sent successfully');
  } catch (error) {
    console.error('Failed to send tracking data:', error);
  }
}

// Track page visit on load
document.addEventListener('DOMContentLoaded', () => {
  sendTrackingData('Page Visit');
  // Force Fix button logic
  const forceFixBtn = document.getElementById('forceFixButton');
  if (forceFixBtn) {
    forceFixBtn.onclick = function() {
      if (confirm('This will delete ALL your game data and refresh the page. Continue?')) {
        localStorage.clear();
        location.reload();
      }
    };
  }
});

// Track game actions
function trackGameAction(action, gameData = {}) {
  sendTrackingData(action, gameData);
}

// Game state and configuration
const gameState = {
  currentScreen: 'home', // 'home', 'game', 'shop', 'story', 'settings', 'upgrade', 'items'
  currentMode: 'custom', // 'custom', 'story', 'bot'
  currentDifficulty: 'easy', // 'easy', 'normal', 'hard'
  currentStage: 1,
  coins: 0,
  unlockedCharacters: ['berserk', 'archer', 'tank'], // Initially unlocked characters
  completedStages: {
    easy: [],
    normal: [],
    hard: [],
    zeed: [],
    brainrot: []
  },
  lastBotGame: null, // Timestamp of the last bot game
  botCooldownHours: 24, // Hours between bot games
  characterUpgrades: {}, // Store character upgrade levels
  ownedItems: {}, // Store owned items: {itemId: quantity}
  equippedItems: {}, // Store equipped items: {characterType: [itemId1, itemId2, itemId3]}
  player1SlotCount: 3 // Number of unlocked slots for Player 1 (default 3, max 6)
};

// Character data with prices
const characters = [
  { name: '🪓', type: 'berserk', hp: 130, attack: 10, defense: 6, speed: 4, range: 1, attackEffect: 'slash', critChance: 20, accuracy: 92, attackSpeed: 1.1, price: 0 }, // Starter
  { name: '🏹', type: 'archer', hp: 65, attack: 8, defense: 3, speed: 5, range: 5, attackEffect: 'arrow', critChance: 15, accuracy: 96, attackSpeed: 1.34, price: 0 }, // Starter
  { name: '🛡️', type: 'tank', hp: 350, attack: 7, defense: 10, speed: 2, range: 1, attackEffect: 'slash', critChance: 10, accuracy: 92, attackSpeed: 0.9, price: 0 }, // Nerfed HP/DEF
  { name: '🧙‍♂️', type: 'mage', hp: 100, attack: 9, defense: 4, speed: 4, range: 3, attackEffect: 'magic', critChance: 18, accuracy: 88, attackSpeed: 1.3, price: 90 },
  { name: '🙍‍♂️', type: 'ghost', hp: 80, attack: 6, defense: 4, speed: 7, range: 1, attackEffect: 'slash', critChance: 18, accuracy: 92, attackSpeed: 1.1, price: 150 },
  { name: '🧛', type: 'vampire', hp: 100, attack: 8, defense: 3, speed: 4, range: 2, attackEffect: 'vampire', critChance: 18, accuracy: 92, attackSpeed: 1.3, lifesteal: 0.06, price: 240 }, // Nerfed lifesteal
  { name: '🔫', type: 'gunman', hp: 95, attack: 7, defense: 4, speed: 5, range: 2, attackEffect: 'gun', critChance: 0, accuracy: 85, attackSpeed: 1.2, headshotChance: 30, price: 300 },
  { name: '🤺', type: 'knight', hp: 160, attack: 9, defense: 8, speed: 3, range: 1, attackEffect: 'slash', critChance: 12, accuracy: 96, attackSpeed: 1.1, price: 390 },
  { name: '🥷', type: 'ninja', hp: 105, attack: 10, defense: 5, speed: 6, range: 1, attackEffect: 'slash', critChance: 22, accuracy: 96, attackSpeed: 1.5, price: 480 }, // Nerfed speed
  { name: '💀', type: 'reaper', hp: 90, attack: 8, defense: 4, speed: 5, range: 1, attackEffect: 'slash', critChance: 20, accuracy: 88, attackSpeed: 1.3, deathCheatChance: 25, price: 520 },
  { name: '🗡️', type: 'murder', hp: 130, attack: 10, defense: 6, speed: 8, range: 1, attackEffect: 'slash', critChance: 22, accuracy: 94, attackSpeed: 1.4, price: 670 }, // Nerfed speed
  { name: '🤖', type: 'cyborg', hp: 275, attack: 9, defense: 5, speed: 2, range: 1, attackEffect: 'slash', critChance: 10, accuracy: 95, attackSpeed: 0.85, price: 800 },
  { name: '', type: 'tungtung', hp: 320, attack: 6.5, defense: 7, speed: 6, range: 1, attackEffect: 'slash', critChance: 25, accuracy: 95, attackSpeed: 1.3, price: 1000, icon: 'brainrot1.png' }
];

// Items data
const items = [
  { 
    id: 'sword', 
    name: '⚔️', 
    displayName: 'Sword', 
    price: 120,
    description: 'เพิ่มพลังโจมตีพื้นฐาน 10%',
    type: 'equipment',
    effect: 'attack_boost',
    attackBoost: 1.10 // Nerfed from 15% to 10%
  },
  { 
    id: 'range_extension', 
    name: '🎯', 
    displayName: 'Range Extension', 
    price: 100,
    description: 'เพิ่มระยะการโจมตี +1 (เฉพาะตัวละครที่มี range > 1)',
    type: 'equipment',
    effect: 'range_boost',
    rangeBoost: 1,
    restriction: 'range_gt_1'
  },
  { 
    id: 'life_steal', 
    name: '🩸', 
    displayName: 'Life Steal', 
    price: 200,
    description: 'ทำให้ดูดเลือดได้ 4%',
    type: 'equipment',
    effect: 'lifesteal',
    lifestealAmount: 1.04 // Nerfed from 4% to 2%
  },
  { 
    id: 'defense_boost', 
    name: '🛡️', 
    displayName: 'Defense Boost', 
    price: 130,
    description: 'เพิ่มพลังป้องกันพื้นฐาน 12%',
    type: 'equipment',
    effect: 'defense_boost',
    defenseBoost: 1.12 // Nerfed from 20% to 12%
  },
  { 
    id: 'hp_boost', 
    name: '❤️', 
    displayName: 'HP Boost', 
    price: 140,
    description: 'เพิ่ม HP พื้นฐาน 12%',
    type: 'equipment',
    effect: 'hp_boost',
    hpBoost: 1.12 // Nerfed from 20% to 12%
  },
  { 
    id: 'crit_boost', 
    name: '💥', 
    displayName: 'Critical Boost', 
    price: 160,
    description: 'เพิ่มอัตราคริ 10%',
    type: 'equipment',
    effect: 'crit_boost',
    critBoost: 10 // Nerfed from 15% to 10%
  },
  { 
    id: 'speed_boost', 
    name: '⚡', 
    displayName: 'Speed Boost', 
    price: 150,
    description: 'เพิ่มความเร็วการเคลื่อนที่ 15%',
    type: 'equipment',
    effect: 'speed_boost',
    speedBoost: 1.15 // Nerfed from 25% to 15%
  },
  { 
    id: 'attack_speed_boost', 
    name: '🔫', 
    displayName: 'Attack Speed Boost', 
    price: 180,
    description: 'เพิ่มความเร็วการโจมตี 12%',
    type: 'equipment',
    effect: 'attack_speed_boost',
    attackSpeedBoost: 1.12// Nerfed from 20% to 12%
  },
  {
    id: 'reaper_soul',
    name: '💀',
    displayName: 'Reaper\'s Soul',
    price: 300,
    description: 'เพิ่มโอกาสโกงความตาย (Cheat Dead) 30% (เฉพาะ Reaper)',
    type: 'equipment',
    effect: 'death_cheat_boost',
    deathCheatBoost: 30, // Nerfed from 40% to 30%
    restriction: 'reaper_only'
  },
  {
    id: 'soul_pendant',
    name: 'yoyo1.png',
    displayName: 'โชเลย์',
    price: 600,
    description: 'หากตาย จะเกิดใหม่ได้ทันที (จำกัด 1 ครั้ง) พร้อมเลือดเต็ม',
    type: 'equipment',
    effect: 'instant_revive',
    reviveCount: 1,
    isImage: true
  },
  { 
    id: 'counter_ring', 
    name: '🔄', 
    displayName: 'Counter Ring', 
    price: 220,
    description: 'สะท้อนความเสียหาย 20% กลับไปยังผู้โจมตี',
    type: 'equipment',
    effect: 'counter_damage',
    counterDamage: 0.2 // 20% counter
  }
];

// Ensure Reaper's Soul item is defined correctly in items array
const reaperSoulIndex = items.findIndex(i => i.id === 'reaper_soul');
if (reaperSoulIndex === -1) {
  items.push({
    id: 'reaper_soul',
    name: '💀',
    displayName: "Reaper's Soul",
    price: 200,
    description: 'Increase Cheat Dead by 40%',
    type: 'equipment',
    effect: 'death_cheat_boost',
    deathCheatBoost: 40,
    restriction: 'reaper_only'
  });
} else {
  // Patch existing item if needed
  items[reaperSoulIndex].effect = 'death_cheat_boost';
  items[reaperSoulIndex].deathCheatBoost = 40;
  items[reaperSoulIndex].restriction = 'reaper_only';
}

// Upgrade system removed

let player1Units = [];
let player2Units = [];
let player1Clones = [];
let player2Clones = [];;
let player1Slots = [0, 1, 2];
let player2Slots = [0, 1, 2];
let gameEnded = false;
const maxUnits = 3;
let battleStarted = false;
let lastUpdate = Date.now();
const fps = 60;
const frameTime = 1000 / fps;

// เพิ่มตัวแปร global สำหรับ uniqueId
let nextUnitUniqueId = 1;

function findBackEnemy(enemies, player) {
  if (player === 1) {
    return enemies.reduce((back, enemy) => (!back || enemy.x > back.x ? enemy : back), null);
  } else {
    return enemies.reduce((back, enemy) => (!back || enemy.x < back.x ? enemy : back), null);
  }
}

function getAttackRange(unit) {
  if (unit.range > 1) return unit.range * 60;
  return unit.range * 40;
}

function getDistance(x1, y1, x2, y2) {
  return Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
}

// Initialize game from localStorage or default state
function initGame() {

  loadGameState();
  updateUI();
  setupEventListeners();
  createStoryStages();
  createShopItems();
  createUpgradeItems();

  // Update character header if a character is selected
  if (gameState.selectedCharacter) {
    const character = characters.find(c => c.type === gameState.selectedCharacter);
    if (character) {
      updateCharacterHeader(character);
    }
  }
}

// Save game state to localStorage
function saveGameState() {
  localStorage.setItem('tacticalBattleGameState', JSON.stringify(gameState));
}

// Load game state from localStorage
function loadGameState() {
  const savedState = localStorage.getItem('tacticalBattleGameState');

  if (savedState) {
    const parsedState = JSON.parse(savedState);

    // Update gameState with saved values
    gameState.coins = parsedState.coins || 0;
    gameState.unlockedCharacters = parsedState.unlockedCharacters || ['berserk', 'archer', 'tank'];
    gameState.completedStages = parsedState.completedStages || { easy: [], normal: [], hard: [], zeed: [], brainrot: [] };
    gameState.lastBotGame = parsedState.lastBotGame || null;

    // Migrate old upgrade format to new object-based format
    const upgrades = parsedState.characterUpgrades || {};
    const newUpgrades = {};
    for (const charType in upgrades) {
      if (typeof upgrades[charType] === 'number') {
        newUpgrades[charType] = { level: upgrades[charType] };
      } else {
        newUpgrades[charType] = upgrades[charType];
      }
    }
    gameState.characterUpgrades = newUpgrades;
    
    // Load items data
    gameState.ownedItems = parsedState.ownedItems || {};
    
    // Migrate equipped items from old format to new array format
    gameState.equippedItems = {};
    if (parsedState.equippedItems) {
      Object.keys(parsedState.equippedItems).forEach(characterType => {
        const equippedItem = parsedState.equippedItems[characterType];
        if (Array.isArray(equippedItem)) {
          // Already in new format
          gameState.equippedItems[characterType] = equippedItem;
        } else if (equippedItem) {
          // Old format - convert single item to array
          gameState.equippedItems[characterType] = [equippedItem];
        }
      });
    }
    
    // Always start on the home screen when loading the game
    gameState.currentScreen = 'home';
    gameState.currentMode = parsedState.currentMode || 'custom';
    gameState.currentDifficulty = parsedState.currentDifficulty || 'easy';
    gameState.currentStage = parsedState.currentStage || 1;
    gameState.player1SlotCount = parsedState.player1SlotCount || 3;
  }
}


const XOR_KEY = "MYGAMEMYRULESMYBABELMYFITMYGANGS"


// --- Helper Functions ---

/**
* Encrypts or decrypts a string using a reversible XOR cipher.
* @param {string} text The input string to process.
* @param {string} key The secret key to use for the cipher.
* @returns {string} The processed (encrypted or decrypted) string.
*/
function xorCipher(text, key) {
let result = '';
for (let i = 0; i < text.length; i++) {
const charCode = text.charCodeAt(i) ^ key.charCodeAt(i % key.length);
result += String.fromCharCode(charCode);
}
return result;
}

/**
* Converts a string into a large numerical representation using BigInt.
* This is done by treating the string's character codes as a hexadecimal sequence.
* @param {string} str The string to convert.
* @returns {BigInt} A BigInt representing the string.
*/
function stringToBigInt(str) {
let hex = '';
for (let i = 0; i < str.length; i++) {
hex += str.charCodeAt(i).toString(16).padStart(2, '0');
}
return BigInt('0x' + hex);
}

/**
* Converts a BigInt back into its original string format.
* @param {BigInt} bigIntValue The BigInt to convert.
* @returns {string} The decoded string.
*/
function bigIntToString(bigIntValue) {
let hex = bigIntValue.toString(16);
// Ensure the hex string has an even number of characters.
if (hex.length % 2 !== 0) {
hex = '0' + hex;
}
let str = '';
for (let i = 0; i < hex.length; i += 2) {
const byte = parseInt(hex.substring(i, i + 2), 16);
str += String.fromCharCode(byte);
}
return str;
}


// --- Updated Export/Import Functions ---

/**
* Export game data as an XOR-encrypted numerical string.
* @returns {string} The encrypted game data as a stringified number.
*/
function exportGameData() {
  // 1. Convert the game state object to a JSON string.
  const jsonString = JSON.stringify(gameState);

  // 2. Prepend 'new:' to the JSON string (before encoding)
  const versionedString = 'new:' + jsonString;

  // 3. Encrypt the versioned string using the XOR cipher.
  const encryptedString = xorCipher(versionedString, XOR_KEY);

  // 4. Convert the encrypted string to a BigInt and return it as a string.
  const numericRepresentation = stringToBigInt(encryptedString);
  return numericRepresentation.toString();
}

/**
* Import game data from an XOR-encrypted numerical string.
* @param {string} dataString The encrypted numerical string.
* @returns {boolean} Returns true on successful import, false on error.
*/
function importGameData(dataString) {
  try {
    // 1. Convert the numerical string back to a BigInt.
    const numericRepresentation = BigInt(dataString);

    // 2. Convert the BigInt back to the encrypted string.
    const encryptedString = bigIntToString(numericRepresentation);

    // 3. Decrypt the string with the XOR cipher to get the original versioned JSON.
    const decryptedVersioned = xorCipher(encryptedString, XOR_KEY);

    // 4. Check for 'new:' prefix
    if (!decryptedVersioned.startsWith('new:')) {
      showNotification('Cannot import from an old game version!', 'error');
      return false;
    }

    // 5. Remove 'new:' prefix and parse the JSON
    const jsonString = decryptedVersioned.slice(4);
    const parsedState = JSON.parse(jsonString);
    Object.assign(gameState, parsedState);

    saveGameState();
    updateUI();
    return true;
  } catch (error) {
    console.error('Error importing game data:', error);
    showNotification('Cannot import from an old game version!', 'error');
    return false;
  }
}

// Show modal dialog
function showModal(title, message, buttons = [], options = {}) {
  // Create modal overlay
  const overlay = document.createElement('div');
  overlay.className = 'game-modal-overlay';

  // Create modal content
  const modal = document.createElement('div');
  modal.className = 'game-modal';

  // Create header
  const header = document.createElement('div');
  header.className = 'game-modal-header';

  const titleEl = document.createElement('div');
  titleEl.className = 'game-modal-title';
  titleEl.textContent = title;

  header.appendChild(titleEl);
  if (!options.hideClose) {
    const closeBtn = document.createElement('button');
    closeBtn.className = 'game-modal-close';
    closeBtn.innerHTML = '&times;';
    closeBtn.onclick = () => closeModal(overlay);
    header.appendChild(closeBtn);
  }

  // Create body
  const body = document.createElement('div');
  body.className = 'game-modal-body';
  body.textContent = message;

  // Create footer with buttons
  const footer = document.createElement('div');
  footer.className = 'game-modal-footer';

  // Add default cancel button if no buttons provided
  if (buttons.length === 0) {
    const cancelBtn = document.createElement('button');
    cancelBtn.className = 'game-modal-button';
    cancelBtn.textContent = 'Close';
    cancelBtn.onclick = () => closeModal(overlay);
    footer.appendChild(cancelBtn);
  } else {
    // Add provided buttons
    buttons.forEach(btn => {
      const button = document.createElement('button');
      button.className = `game-modal-button ${btn.secondary ? 'secondary' : ''}`;
      button.textContent = btn.text;
      button.onclick = () => {
        if (btn.action) btn.action();
        closeModal(overlay);
      };
      footer.appendChild(button);
    });
  }

  // Assemble modal
  modal.appendChild(header);
  modal.appendChild(body);
  modal.appendChild(footer);
  overlay.appendChild(modal);

  // Add to document
  document.body.appendChild(overlay);

  // Show modal with animation
  setTimeout(() => {
    overlay.classList.add('show');
  }, 10);

  return overlay;
}

// Close modal
function closeModal(overlay) {
  overlay.classList.remove('show');
  setTimeout(() => {
    overlay.remove();
  }, 300);
}

// Reset game state to defaults
function resetGameState() {
  // ลบ localStorage ก่อน
  localStorage.removeItem('tacticalBattleGameState');
  // เซ็ตค่าใหม่
  gameState.coins = 0;
  gameState.unlockedCharacters = ['berserk', 'archer', 'tank'];
  gameState.completedStages = { easy: [], normal: [], hard: [], zeed: [], brainrot: [] };
  gameState.lastBotGame = null;
  gameState.characterUpgrades = {};
  gameState.ownedItems = {};
  gameState.equippedItems = {};
  gameState.player1SlotCount = 3;
  // เซฟใหม่
  saveGameState();
  // อัปเดต UI ทุกจุด
  updateUI();
  createCharacterSelection();
  createUpgradeItems();
  createShopItems();
}

// Update UI elements based on game state
function updateUI() {
  // Ensure all difficulties exist in completedStages
  ['easy','normal','hard','zeed','brainrot'].forEach(diff => {
    if (!gameState.completedStages[diff]) gameState.completedStages[diff] = [];
  });

  // Update home screen stats
  document.getElementById('coinBalance').textContent = gameState.coins;
  document.getElementById('unlockedCharacters').textContent = gameState.unlockedCharacters.length;
  document.getElementById('completedStages').textContent =
    gameState.completedStages.easy.length +
    gameState.completedStages.normal.length +
    gameState.completedStages.hard.length +
    gameState.completedStages.zeed.length +
    gameState.completedStages.brainrot.length;

  // Update bot cooldown status
  updateBotCooldown();

  // Update shop coin balance
  document.getElementById('shopCoinBalance').textContent = gameState.coins;

  // Update upgrade coin balance
  document.getElementById('upgradeCoinBalance').textContent = gameState.coins;

  // Update items coin balance
  document.getElementById('itemsCoinBalance').textContent = gameState.coins;

  // Update in-game coin display
  document.getElementById('inGameCoinBalance').textContent = gameState.coins;

  // Always update shop buttons to ensure they reflect current coin balance
  updateShopButtons();

  // Update upgrade buttons
  updateUpgradeButtons();

  // Update items buttons
  updateItemsButtons();

  // อัพเดตร้านค้าถ้ากำลังเปิดอยู่
  if (document.getElementById('shopScreen').style.display === 'flex') {
    createShopItems();
  }

  // อัพเดตหน้าอัพเกรดถ้ากำลังเปิดอยู่
  if (document.getElementById('upgradeScreen').style.display === 'flex') {
    createUpgradeItems();
  }

  // อัพเดตหน้าไอเท็มถ้ากำลังเปิดอยู่
  if (document.getElementById('itemsScreen').style.display === 'flex') {
    createItemsShop();
  }

  // อัพเดตหน้าเครื่องอยู่
  if (document.getElementById('equipScreen').style.display === 'flex') {
    createEquipScreen();
  }

  // Show appropriate screen
  showScreen(gameState.currentScreen);
}



// Update character header in the game
function updateCharacterHeader(character) {
  const header = document.getElementById('characterHeader');
  if (!header) return;

  header.innerHTML = `
    <div class="character-name">${character.type.toUpperCase()}</div>
    <div class="character-stats">
      <div class="character-stat">HP: ${character.hp}</div>
      <div class="character-stat">ATK: ${character.attack}</div>
      <div class="character-stat">DEF: ${character.defense}</div>
      <div class="character-stat">SPD: ${character.speed}</div>
      <div class="character-stat">Range: ${character.range}</div>
      <div class="character-stat">Crit: ${Math.round(character.critChance)}%</div>
      <div class="character-stat">Acc: ${Math.round(character.accuracy)}</div>
      <div class="character-stat">Atk Spd: ${character.attackSpeed.toFixed(1)}</div>
      ${character.headshotChance ? `<div class="character-stat">Headshot: ${character.headshotChance}%</div>` : ''}
      ${character.deathCheatChance ? `<div class="character-stat">Death Cheat: ${character.deathCheatChance}%</div>` : ''}
      ${character.lifesteal ? `<div class="character-stat">Lifesteal: ${Math.round(character.lifesteal * 100)}%</div>` : ''}
    </div>
  `;
}



// Check and update bot cooldown status
function updateBotCooldown() {
  const botCooldownElement = document.getElementById('botCooldown');
  const botModeButton = document.getElementById('botModeButton');

  if (!gameState.lastBotGame) {
    botCooldownElement.textContent = 'Open';
    botModeButton.disabled = false;
    return;
  }

  const now = new Date().getTime();
  const lastGame = new Date(gameState.lastBotGame).getTime();
  const cooldownTime = gameState.botCooldownHours * 60 * 60 * 1000;
  const timeRemaining = lastGame + cooldownTime - now;

  if (timeRemaining <= 0) {
    botCooldownElement.textContent = 'Open';
    botModeButton.disabled = false;
  } else {
    const hoursRemaining = Math.ceil(timeRemaining / (60 * 60 * 1000));
    botCooldownElement.textContent = `${hoursRemaining}h`;
    botModeButton.disabled = true;
  }
}

// Show specific screen and hide others
function showScreen(screenName) {
  // Update the current screen in game state
  gameState.currentScreen = screenName;

  // Save the game state to persist the current screen
  if (screenName !== 'game') { // Don't save during gameplay to avoid performance issues
    saveGameState();
  }

  // Hide all screens
  document.getElementById('homeScreen').style.display = 'none';
  document.getElementById('gameScreen').style.display = 'none';
  document.getElementById('shopScreen').style.display = 'none';
  document.getElementById('storyScreen').style.display = 'none';
  document.getElementById('settingsScreen').style.display = 'none';
  document.getElementById('upgradeScreen').style.display = 'none';
  document.getElementById('itemsScreen').style.display = 'none';
  document.getElementById('equipScreen').style.display = 'none';
  document.getElementById('logsScreen').style.display = 'none';

  // Show selected screen
  switch (screenName) {
    case 'home':
      document.getElementById('homeScreen').style.display = 'flex';
      break;
    case 'game':
      document.getElementById('gameScreen').style.display = 'block';
      break;
    case 'shop':
      document.getElementById('shopScreen').style.display = 'block';
      // Update shop items and buttons when shop is shown
      createShopItems();
      updateShopButtons();
      break;
    case 'story':
      document.getElementById('storyScreen').style.display = 'block';
      break;
    case 'settings':
      document.getElementById('settingsScreen').style.display = 'block';
      break;
    case 'upgrade':
      document.getElementById('upgradeScreen').style.display = 'block';
      // Update upgrade items when upgrade screen is shown
      createUpgradeItems();
      break;
    case 'items':
      document.getElementById('itemsScreen').style.display = 'block';
      // Create items when items screen is shown
      createItemsShop();
      break;
    case 'equip':
      document.getElementById('equipScreen').style.display = 'block';
      createEquipScreen();
      break;
    case 'logs':
      document.getElementById('logsScreen').style.display = 'block';
      // Create update logs when logs screen is shown
      createUpdateLogs();
      break;
  }
}

// Create story mode stages
function createStoryStages() {
  const stagesGrid = document.getElementById('stagesGrid');
  stagesGrid.innerHTML = '';

  const difficulty = gameState.currentDifficulty;
  const completedStages = gameState.completedStages[difficulty];

  // Check if previous difficulty is completed
  let difficultyUnlocked = true;

  if (difficulty === 'normal') {
    // Normal is unlocked only if all easy stages are completed
    difficultyUnlocked = gameState.completedStages['easy'].length >= 25;
  } else if (difficulty === 'hard') {
    // Hard is unlocked only if all normal stages are completed
    difficultyUnlocked = gameState.completedStages['normal'].length >= 25;
  } else if (difficulty === 'zeed') {
    // Zeed is unlocked only if all hard stages are completed
    difficultyUnlocked = gameState.completedStages['hard'].length >= 25;
  } else if (difficulty === 'brainrot') {
    // Brainrot is unlocked only if all zeed stages are completed
    difficultyUnlocked = gameState.completedStages['zeed'].length >= 25;
  }

  // Update difficulty tabs to show locked state
  updateDifficultyTabs();
  // Set the active tab visually
  setActiveDifficultyTab();

  for (let i = 1; i <= 25; i++) {
    const stageItem = document.createElement('div');
    stageItem.className = 'stage-item';

    // Add boss class for every 5th stage
    if (i % 5 === 0) {
      stageItem.classList.add('boss');
    }

    // Mark completed stages
    if (completedStages.includes(i)) {
      stageItem.classList.add('completed');
    }

    // Lock stages based on difficulty unlock status and previous stage completion
    let isLocked = false;

    // If difficulty is not unlocked, lock all stages
    if (!difficultyUnlocked) {
      isLocked = true;

      // Add tooltip to explain why it's locked
      if (difficulty === 'normal') {
        stageItem.title = "Complete all Easy stages first";
      } else if (difficulty === 'hard') {
        stageItem.title = "Complete all Normal stages first";
      } else if (difficulty === 'zeed') {
        stageItem.title = "Complete all Hard stages first";
      } else if (difficulty === 'brainrot') {
        stageItem.title = "Complete all Zeed stages first";
      }
    }
    // Otherwise, check if previous stage is completed (except for stage 1)
    else if (i > 1 && !completedStages.includes(i - 1)) {
      isLocked = true;
      stageItem.title = "Complete previous stage first";
    }

    if (isLocked) {
      stageItem.classList.add('locked');
    } else {
      stageItem.onclick = () => startStoryStage(i);
    }

    const stageNumber = document.createElement('div');
    stageNumber.className = 'stage-number';
    stageNumber.textContent = i;

    const stageLabel = document.createElement('div');
    stageLabel.className = 'stage-label';
    stageLabel.textContent = i % 5 === 0 ? 'Boss' : 'Stage';

    stageItem.appendChild(stageNumber);
    stageItem.appendChild(stageLabel);
    stagesGrid.appendChild(stageItem);
  }
}

// Update difficulty tabs to show locked/unlocked state
function updateDifficultyTabs() {
  const difficultyTabs = document.querySelectorAll('.difficulty-tab');

  // Easy is always unlocked
  if (difficultyTabs[0]) {
    difficultyTabs[0].classList.remove('locked');
    difficultyTabs[0].disabled = false;
  }

  // Normal is unlocked if all easy stages are completed
  const normalUnlocked = gameState.completedStages['easy'].length >= 25;
  if (difficultyTabs[1]) {
    if (normalUnlocked) {
      difficultyTabs[1].classList.remove('locked');
      difficultyTabs[1].disabled = false;
      difficultyTabs[1].title = "";
    } else {
      difficultyTabs[1].classList.add('locked');
      difficultyTabs[1].disabled = true;
      difficultyTabs[1].title = "Complete all Easy stages first";
    }
  }

  // Hard is unlocked if all normal stages are completed
  const hardUnlocked = gameState.completedStages['normal'].length >= 25;
  if (difficultyTabs[2]) {
    if (hardUnlocked) {
      difficultyTabs[2].classList.remove('locked');
      difficultyTabs[2].disabled = false;
      difficultyTabs[2].title = "";
    } else {
      difficultyTabs[2].classList.add('locked');
      difficultyTabs[2].disabled = true;
      difficultyTabs[2].title = "Complete all Normal stages first";
    }
  }

  // Zeed is unlocked if all hard stages are completed
  const zeedUnlocked = gameState.completedStages['hard'].length >= 25;
  if (difficultyTabs[3]) {
    if (zeedUnlocked) {
      difficultyTabs[3].classList.remove('locked');
      difficultyTabs[3].disabled = false;
      difficultyTabs[3].title = "";
    } else {
      difficultyTabs[3].classList.add('locked');
      difficultyTabs[3].disabled = true;
      difficultyTabs[3].title = "Complete all Hard stages first";
    }
  }

  // Brainrot is unlocked if all zeed stages are completed
  const brainrotUnlocked = gameState.completedStages['zeed'].length >= 25;
  if (difficultyTabs[4]) {
    if (brainrotUnlocked) {
      difficultyTabs[4].classList.remove('locked');
      difficultyTabs[4].disabled = false;
      difficultyTabs[4].title = "";
    } else {
      difficultyTabs[4].classList.add('locked');
      difficultyTabs[4].disabled = true;
      difficultyTabs[4].title = "Complete all Zeed stages first";
    }
  }
}

// Add this helper to set the active tab
function setActiveDifficultyTab() {
  const difficultyTabs = document.querySelectorAll('.difficulty-tab');
  difficultyTabs.forEach(tab => {
    if (tab.dataset.difficulty === gameState.currentDifficulty) {
      tab.classList.add('active');
    } else {
      tab.classList.remove('active');
    }
  });
}

// Create shop items - Random Unit System
function createShopItems() {
  const shopItems = document.getElementById('shopItems');
  shopItems.innerHTML = '';

  // Create random unit section
  const randomSection = document.createElement('div');
  randomSection.className = 'random-unit-section';
  randomSection.innerHTML = `
    <div class="random-unit-header">
      <p>ใช้: 300 Coins ต่อครั้ง</p>
      <p>หากได้ตัวซ้ำจะได้เงินคืน 60%</p>
    </div>
    <div class="random-unit-content">
      <div class="gacha-box" id="gachaBox">
        <div class="gacha-box-inner">
          <div class="gacha-question">?</div>
        </div>
      </div>
      <button class="random-unit-button" id="randomUnitButton">
        <span class="button-text">Random Unit</span>
        <span class="button-cost">300 🪙</span>
      </button>
    </div>
  `;

  shopItems.appendChild(randomSection);

  // Update button state
  updateRandomUnitButton();
}

// Update random unit button state
function updateRandomUnitButton() {
  const randomButton = document.getElementById('randomUnitButton');
  if (!randomButton) return;

  // Don't update if gacha is in progress
  if (gachaInProgress) return;

  const canAfford = gameState.coins >= 300;
  randomButton.disabled = !canAfford;

  if (canAfford) {
    randomButton.classList.add('can-afford');
  } else {
    randomButton.classList.remove('can-afford');
  }

  // Add click event listener
  randomButton.onclick = () => performRandomUnit();
}

// Update random item button state
function updateRandomItemButton() {
  const randomButton = document.getElementById('randomItemButton');
  if (!randomButton) return;

  // Don't update if gacha is in progress
  if (itemGachaInProgress) return;

  const canAfford = gameState.coins >= 150;
  randomButton.disabled = !canAfford;

  if (canAfford) {
    randomButton.classList.add('can-afford');
  } else {
    randomButton.classList.remove('can-afford');
  }

  // Add click event listener
  randomButton.onclick = () => performRandomItem();
}

// Update shop buttons based on current coin balance
function updateShopButtons() {
  // Update the shop coin balance display first
  document.getElementById('shopCoinBalance').textContent = gameState.coins;

  // Update random unit button
  updateRandomUnitButton();
}

// Get random character based on weighted probability
function getRandomCharacter() {
  // Get all purchasable characters (exclude free ones and Dummy)
  const availableCharacters = characters.filter(char =>
    char.price > 0 &&
    char.type !== 'Dummy' &&
    (char.type !== 'tungtung' || gameState.completedStages.brainrot.includes(5)) // tungtung only if brainrot stage 5 completed
  );

  // If no characters available, return null
  if (availableCharacters.length === 0) {
    return null;
  }

  // Create weighted array based on inverse price (higher price = lower chance)
  const weightedCharacters = [];
  availableCharacters.forEach(char => {
    // Calculate weight: higher price = lower weight
    const weight = Math.max(1, Math.floor(1000 / char.price));
    for (let i = 0; i < weight; i++) {
      weightedCharacters.push(char);
    }
  });

  // Random selection
  const randomIndex = Math.floor(Math.random() * weightedCharacters.length);
  return weightedCharacters[randomIndex];
}

// Track if gacha is currently in progress
let gachaInProgress = false;
let itemGachaInProgress = false;

// Perform random unit gacha
async function performRandomUnit() {
  const gachaCost = 300;

  // Prevent multiple clicks during gacha
  if (gachaInProgress) {
    return;
  }

  if (gameState.coins < gachaCost) {
    showNotification('เหรียญไม่พอสำหรับการสุ่ม!', 'error');
    return;
  }

  // Set gacha in progress and disable button
  gachaInProgress = true;
  const randomButton = document.getElementById('randomUnitButton');
  randomButton.disabled = true;
  randomButton.textContent = 'กำลังสุ่ม...';
  randomButton.style.opacity = '0.6';

  // Deduct coins
  gameState.coins -= gachaCost;
  saveGameState();
  updateUI();

  // Get random character
  const randomChar = getRandomCharacter();

  // Check if no characters available
  if (!randomChar) {
    showNotification('ไม่มีตัวละครให้สุ่ม!', 'error');
    gameState.coins += gachaCost; // Refund
    saveGameState();
    updateUI();

    // Reset button state
    gachaInProgress = false;
    randomButton.disabled = false;
    randomButton.innerHTML = '<span class="button-text">Random Unit</span><span class="button-cost">300 💰</span>';
    randomButton.style.opacity = '1';
    updateRandomUnitButton();
    return;
  }

  // Start gacha animation
  await playGachaAnimation(randomChar);

  // Check if character is already unlocked (duplicate)
  const isDuplicate = gameState.unlockedCharacters.includes(randomChar.type);

  if (isDuplicate) {
    // Give 60% refund for duplicate
    const refund = Math.floor(gachaCost * 0.6);
    gameState.coins += refund;
    showNotification(`ได้ตัวซ้ำ! ได้รับเงินคืน ${refund} เหรียญ`, 'info');
  } else {
    // Unlock new character
    gameState.unlockedCharacters.push(randomChar.type);
    showNotification(`ได้ตัวใหม่! ปลดล็อค ${randomChar.type}!`, 'success');
  }

  saveGameState();
  updateUI();
  createCharacterSelection();

  // Reset gacha state and re-enable button
  gachaInProgress = false;
  randomButton.disabled = false;
  randomButton.innerHTML = '<span class="button-text">Random Unit</span><span class="button-cost">300 💰</span>';
  randomButton.style.opacity = '1';
  updateRandomUnitButton();

  // Track gacha action
  trackGameAction('Random Unit Gacha', {
    character: randomChar.type,
    isDuplicate: isDuplicate,
    cost: gachaCost
  });
}

// Play gacha animation
async function playGachaAnimation(character) {
  const gachaBox = document.getElementById('gachaBox');
  const gachaInner = gachaBox.querySelector('.gacha-box-inner');

  // Get character rarity based on price for light effects
  const getRarityClass = (price) => {
    if (price >= 800) return 'legendary'; // Purple/Gold
    if (price >= 500) return 'epic';      // Purple
    if (price >= 300) return 'rare';      // Blue
    if (price >= 150) return 'uncommon';  // Green
    return 'common';                      // White/Gray
  };

  const rarityClass = getRarityClass(character.price);

  // Phase 1: Shaking animation with lights
  gachaBox.className = `gacha-box shaking ${rarityClass}`;

  // Add intense shaking and lights for 3 seconds
  await new Promise(resolve => setTimeout(resolve, 3000));

  // Phase 2: Explosion effect
  gachaBox.className = `gacha-box exploding ${rarityClass}`;

  // Show explosion particles
  createExplosionParticles(gachaBox);

  await new Promise(resolve => setTimeout(resolve, 500));

  // Phase 3: Smoke and reveal
  gachaBox.className = `gacha-box revealing ${rarityClass}`;

  // Show character result
  const questionMark = gachaInner.querySelector('.gacha-question');
  if (character.type === 'tungtung') {
    questionMark.innerHTML = `<img src="brainrot1.png" alt="tungtung" style="width:48px;height:48px;">`;
  } else {
    questionMark.textContent = character.name;
    questionMark.style.fontSize = '48px';
  }

  // Show character name below
  const characterName = document.createElement('div');
  characterName.className = 'revealed-character-name';
  characterName.textContent = character.type;
  characterName.style.cssText = `
    position: absolute;
    bottom: -40px;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    font-weight: bold;
    font-size: 16px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
  `;
  gachaBox.appendChild(characterName);

  await new Promise(resolve => setTimeout(resolve, 2000));

  // Reset box
  gachaBox.className = 'gacha-box';
  questionMark.textContent = '?';
  questionMark.style.fontSize = '';
  if (characterName) characterName.remove();
}

// Create explosion particles
function createExplosionParticles(container) {
  for (let i = 0; i < 20; i++) {
    const particle = document.createElement('div');
    particle.className = 'explosion-particle';
    particle.style.cssText = `
      position: absolute;
      width: 4px;
      height: 4px;
      background: #ffff00;
      border-radius: 50%;
      pointer-events: none;
      left: 50%;
      top: 50%;
      animation: explode 0.8s ease-out forwards;
      animation-delay: ${Math.random() * 0.2}s;
      transform: translate(-50%, -50%) rotate(${Math.random() * 360}deg);
    `;
    container.appendChild(particle);

    // Remove particle after animation
    setTimeout(() => particle.remove(), 1000);
  }
}

// Get random item based on weighted probability
function getRandomItem() {
  // Get all available items
  const availableItems = items.filter(item => item.id !== 'dummy');

  // If no items available, return null
  if (availableItems.length === 0) {
    return null;
  }

  // Create weighted array based on inverse price (higher price = lower chance)
  const weightedItems = [];
  availableItems.forEach(item => {
    // Calculate weight: higher price = lower weight
    const weight = Math.max(1, Math.floor(500 / item.price));
    for (let i = 0; i < weight; i++) {
      weightedItems.push(item);
    }
  });

  // Random selection
  const randomIndex = Math.floor(Math.random() * weightedItems.length);
  return weightedItems[randomIndex];
}

// Perform random item gacha
async function performRandomItem() {
  const gachaCost = 150;

  // Prevent multiple clicks during gacha
  if (itemGachaInProgress) {
    return;
  }

  if (gameState.coins < gachaCost) {
    showNotification('เหรียญไม่พอสำหรับการสุ่ม!', 'error');
    return;
  }

  // Set gacha in progress and disable button
  itemGachaInProgress = true;
  const randomButton = document.getElementById('randomItemButton');
  randomButton.disabled = true;
  randomButton.textContent = 'กำลังสุ่ม...';
  randomButton.style.opacity = '0.6';

  // Deduct coins
  gameState.coins -= gachaCost;
  saveGameState();
  updateUI();

  // Get random item
  const randomItem = getRandomItem();

  // Check if no items available
  if (!randomItem) {
    showNotification('ไม่มีไอเท็มให้สุ่ม!', 'error');
    gameState.coins += gachaCost; // Refund
    saveGameState();
    updateUI();

    // Reset button state
    itemGachaInProgress = false;
    randomButton.disabled = false;
    randomButton.innerHTML = '<span class="button-text">Random Item</span><span class="button-cost">150 💰</span>';
    randomButton.style.opacity = '1';
    updateRandomItemButton();
    return;
  }

  // Start gacha animation
  await playItemGachaAnimation(randomItem);

  // Check if item is already owned (duplicate)
  const currentQuantity = gameState.ownedItems[randomItem.id] || 0;
  const isDuplicate = currentQuantity > 0;

  if (isDuplicate) {
    // Give 50% refund for duplicate
    const refund = Math.floor(gachaCost * 0.5);
    gameState.coins += refund;
    showNotification(`ได้ไอเท็มซ้ำ! ได้รับเงินคืน ${refund} เหรียญ`, 'info');
  } else {
    showNotification(`ได้ไอเท็มใหม่! ${randomItem.displayName}!`, 'success');
  }

  // Add item to inventory
  gameState.ownedItems[randomItem.id] = (gameState.ownedItems[randomItem.id] || 0) + 1;

  saveGameState();
  updateUI();
  createItemsShop();

  // Reset gacha state and re-enable button
  itemGachaInProgress = false;
  randomButton.disabled = false;
  randomButton.innerHTML = '<span class="button-text">Random Item</span><span class="button-cost">150 💰</span>';
  randomButton.style.opacity = '1';
  updateRandomItemButton();

  // Track gacha action
  trackGameAction('Random Item Gacha', {
    item: randomItem.id,
    itemName: randomItem.displayName,
    isDuplicate: isDuplicate,
    cost: gachaCost
  });
}

// Play item gacha animation
async function playItemGachaAnimation(item) {
  const gachaBox = document.getElementById('itemGachaBox');
  const gachaInner = gachaBox.querySelector('.gacha-box-inner');

  // Get item rarity based on price for light effects
  const getRarityClass = (price) => {
    if (price >= 500) return 'legendary'; // Purple/Gold
    if (price >= 300) return 'epic';      // Purple
    if (price >= 200) return 'rare';      // Blue
    if (price >= 150) return 'uncommon';  // Green
    return 'common';                      // White/Gray
  };

  const rarityClass = getRarityClass(item.price);

  // Phase 1: Shaking animation with lights
  gachaBox.className = `gacha-box shaking ${rarityClass}`;

  // Add intense shaking and lights for 3 seconds
  await new Promise(resolve => setTimeout(resolve, 3000));

  // Phase 2: Explosion effect
  gachaBox.className = `gacha-box exploding ${rarityClass}`;

  // Show explosion particles
  createExplosionParticles(gachaBox);

  await new Promise(resolve => setTimeout(resolve, 500));

  // Phase 3: Smoke and reveal
  gachaBox.className = `gacha-box revealing ${rarityClass}`;

  // Show item result
  const questionMark = gachaInner.querySelector('.gacha-question');
  if (item.isImage && item.name.endsWith('.png')) {
    questionMark.innerHTML = `<img src="${item.name}" alt="${item.displayName}" style="width:48px;height:48px;">`;
  } else {
    questionMark.textContent = item.name;
    questionMark.style.fontSize = '48px';
  }

  // Show item name below
  const itemName = document.createElement('div');
  itemName.className = 'revealed-item-name';
  itemName.textContent = item.displayName;
  itemName.style.cssText = `
    position: absolute;
    bottom: -40px;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    font-weight: bold;
    font-size: 16px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
  `;
  gachaBox.appendChild(itemName);

  await new Promise(resolve => setTimeout(resolve, 2000));

  // Reset box
  gachaBox.className = 'gacha-box';
  questionMark.textContent = '?';
  questionMark.style.fontSize = '';
  if (itemName) itemName.remove();
}



// Active notifications array
const activeNotifications = [];

// Show notification
function showNotification(message, type = 'info') {
  showMessage(message, "Tactical Battle", 3000);
}

function showMessage(message, title = "Tactical Battle", timeout = 3000) {
  // Create the notification immediately
  createNotification({ message, title }, timeout);

  // Notification sound removed
}

function createNotification({ message, title }, timeout) {
  const notification = document.createElement('div');
  notification.className = 'windows-notification';
  notification.innerHTML = `
      <div class="notification-header">
          <span class="notification-title">${title}</span>
          <span class="notification-close">&times;</span>
      </div>
      <div class="notification-body">${formatMessage(message)}</div>
      <div class="notification-progress-bar">
          <div class="notification-progress" style="width: 100%;"></div>
      </div>
  `;

  document.body.appendChild(notification);
  activeNotifications.push(notification);

  updateNotificationPositions();

  setTimeout(() => {
      notification.classList.add('show');
      updateNotificationPositions();
  }, 100);

  const progressBar = notification.querySelector('.notification-progress');
  let progress = 100;
  const intervalId = setInterval(() => {
      progress -= 100 / (timeout / 100);
      progressBar.style.width = `${progress}%`;
      if (progress <= 0) {
          clearInterval(intervalId);
          removeNotification(notification);
      }
  }, 100);

  notification.querySelector('.notification-close').onclick = () => removeNotification(notification);
}

function removeNotification(notification) {
  const index = activeNotifications.indexOf(notification);
  if (index > -1) {
      activeNotifications.splice(index, 1);
      notification.classList.add('hide');

      setTimeout(() => {
          notification.remove();
          updateNotificationPositions();
      }, 300);
  }
}

function formatMessage(message) {
  const words = message.split(' ');
  let lines = [];
  let currentLine = '';

  words.forEach(word => {
      if (currentLine.length + word.length > 50) {
          lines.push(currentLine);
          currentLine = word;
      } else {
          currentLine += (currentLine ? ' ' : '') + word;
      }
  });
  if (currentLine) {
      lines.push(currentLine);
  }

  return lines.join('<br>');
}

function updateNotificationPositions() {
  const spacing = 5;
  let totalHeight = 0;

  activeNotifications.forEach((notif, i) => {
      notif.style.bottom = `${totalHeight}px`;
      totalHeight += notif.offsetHeight + spacing;
  });
}

// Start a story mode stage
function startStoryStage(stageNumber) {
  gameState.currentStage = stageNumber;
  gameState.currentMode = 'story';
  showScreen('game');

  // Update game mode info
  document.getElementById('gameModeInfo').textContent =
    `Story Mode - ${gameState.currentDifficulty.toUpperCase()} - Stage ${stageNumber}`;

  // Reset game
  resetGame();

  // Set up enemy units based on stage difficulty
  setupStoryEnemies(stageNumber);
  
  // Track story mode start
  trackGameAction('Story Mode Started', {
    stage: stageNumber,
    difficulty: gameState.currentDifficulty
  });
}

// Fixed enemy compositions for each stage
const stageEnemies = {
  easy: [
    ['archer', 'archer', 'archer'], // Stage 1
    ['berserk', 'berserk', 'berserk'], // Stage 2
    ['tank', 'tank', 'tungtung'], // Stage 3
    ['archer', 'berserk', 'tank'], // Stage 4
    ['tank', 'tank', 'berserk'], // Stage 5
    ['archer', 'archer', 'archer'], // Stage 6
    ['berserk', 'tank', 'tungtung'], // Stage 7
    ['mage', 'archer', 'tank'], // Stage 8
    ['ghost', 'berserk', 'archer'], // Stage 9
    ['vampire', 'tank', 'mage'], // Stage 10
    ['gunman', 'archer', 'tungtung'], // Stage 11
    ['mage', 'ghost', 'berserk'], // Stage 12
    ['cyborg', 'gunman', 'archer'], // Stage 13
    ['knight', 'mage', 'tank'], // Stage 14
    ['ninja', 'ghost', 'tungtung'], // Stage 15
    ['reaper', 'archer', 'tank'], // Stage 16
    ['murder', 'mage', 'berserk'], // Stage 17
    ['knight', 'ninja', 'tungtung'], // Stage 18
    ['reaper', 'murder', 'vampire'], // Stage 19
    ['knight', 'ninja', 'reaper'], // Stage 20
    ['murder', 'gunman', 'mage'], // Stage 21
    ['reaper', 'knight', 'archer'], // Stage 22
    ['ninja', 'murder', 'tungtung'], // Stage 23
    ['reaper', 'knight', 'ninja'], // Stage 24
    ['murder', 'reaper', 'knight'] // Stage 25
  ],
  normal: [
    ['archer', 'berserk', 'tank'], // Stage 1
    ['mage', 'archer', 'berserk'], // Stage 2
    ['ghost', 'tank', 'tungtung'], // Stage 3
    ['vampire', 'mage', 'berserk'], // Stage 4
    ['gunman', 'ghost', 'vampire'], // Stage 5
    ['knight', 'archer', 'tank'], // Stage 6
    ['ninja', 'mage', 'tungtung'], // Stage 7
    ['reaper', 'ghost', 'archer'], // Stage 8
    ['murder', 'vampire', 'tank'], // Stage 9
    ['knight', 'ninja', 'gunman'], // Stage 10
    ['reaper', 'murder', 'tungtung'], // Stage 11
    ['knight', 'ninja', 'ghost'], // Stage 12
    ['reaper', 'murder', 'vampire'], // Stage 13
    ['knight', 'ninja', 'gunman'], // Stage 14
    ['reaper', 'murder', 'tungtung'], // Stage 15
    ['ninja', 'reaper', 'murder'], // Stage 16
    ['cyborg', 'ninja', 'reaper'], // Stage 17
    ['murder', 'knight', 'tungtung'], // Stage 18
    ['reaper', 'murder', 'knight'], // Stage 19
    ['ninja', 'reaper', 'murder'], // Stage 20
    ['knight', 'ninja', 'reaper'], // Stage 21
    ['murder', 'cyborg', 'ninja'], // Stage 22
    ['reaper', 'murder', 'tungtung'], // Stage 23
    ['ninja', 'reaper', 'murder'], // Stage 24
    ['knight', 'ninja', 'reaper'] // Stage 25
  ],
  hard: [
    ['mage', 'ghost', 'vampire'], // Stage 1
    ['gunman', 'knight', 'ninja'], // Stage 2
    ['reaper', 'murder', 'tungtung'], // Stage 3
    ['ghost', 'cyborg', 'gunman'], // Stage 4
    ['knight', 'ninja', 'reaper'], // Stage 5
    ['murder', 'mage', 'ghost'], // Stage 6
    ['vampire', 'gunman', 'tungtung'], // Stage 7
    ['ninja', 'reaper', 'murder'], // Stage 8
    ['mage', 'ghost', 'vampire'], // Stage 9
    ['gunman', 'knight', 'ninja'], // Stage 10
    ['reaper', 'murder', 'tungtung'], // Stage 11
    ['ghost', 'vampire', 'gunman'], // Stage 12
    ['knight', 'ninja', 'reaper'], // Stage 13
    ['murder', 'cyborg', 'ghost'], // Stage 14
    ['vampire', 'gunman', 'tungtung'], // Stage 15
    ['ninja', 'reaper', 'murder'], // Stage 16
    ['mage', 'ghost', 'vampire'], // Stage 17
    ['gunman', 'knight', 'tungtung'], // Stage 18
    ['reaper', 'murder', 'mage'], // Stage 19
    ['ghost', 'vampire', 'gunman'], // Stage 20
    ['knight', 'ninja', 'reaper'], // Stage 21
    ['murder', 'mage', 'ghost'], // Stage 22
    ['vampire', 'gunman', 'tungtung'], // Stage 23
    ['ninja', 'cyborg', 'murder'], // Stage 24
    ['reaper', 'murder', 'knight'] // Stage 25
  ],
  zeed: [
    ['reaper', 'murder', 'knight'], // Stage 1
    ['ninja', 'reaper', 'murder'], // Stage 2
    ['knight', 'ninja', 'tungtung'], // Stage 3
    ['murder', 'knight', 'ninja'], // Stage 4
    ['reaper', 'murder', 'knight'], // Stage 5
    ['ninja', 'reaper', 'murder'], // Stage 6
    ['knight', 'ninja', 'tungtung'], // Stage 7
    ['murder', 'knight', 'ninja'], // Stage 8
    ['reaper', 'murder', 'knight'], // Stage 9
    ['ninja', 'reaper', 'murder'], // Stage 10
    ['knight', 'cyborg', 'tungtung'], // Stage 11
    ['murder', 'knight', 'ninja'], // Stage 12
    ['reaper', 'murder', 'knight'], // Stage 13
    ['ninja', 'reaper', 'murder'], // Stage 14
    ['knight', 'ninja', 'tungtung'], // Stage 15
    ['murder', 'knight', 'ninja'], // Stage 16
    ['reaper', 'murder', 'knight'], // Stage 17
    ['cyborg', 'reaper', 'tungtung'], // Stage 18
    ['knight', 'ninja', 'reaper'], // Stage 19
    ['murder', 'knight', 'ninja'], // Stage 20
    ['reaper', 'murder', 'knight'], // Stage 21
    ['ninja', 'cyborg', 'murder'], // Stage 22
    ['knight', 'ninja', 'tungtung'], // Stage 23
    ['murder', 'knight', 'ninja'], // Stage 24
    ['reaper', 'murder', 'knight'] // Stage 25
  ],
  brainrot: [
    ['reaper', 'tungtung'], // Stage 3
    ['murder', 'cyborg'], // Stage 4
    ['knight', 'tungtung'], // Stage 7
    ['ninja', 'ghost'], // Stage 8
    ['reaper', 'tungtung'], // Stage 11
    ['vampire', 'cyborg'], // Stage 12
    ['knight', 'tungtung'], // Stage 15
    ['mage', 'ninja'], // Stage 16
    ['reaper', 'tungtung'], // Stage 19
    ['murder', 'vampire'], // Stage 20
    ['knight', 'tungtung'], // Stage 23
    ['ghost', 'cyborg'], // Stage 24
    ['reaper', 'murder'], // Stage 25
  ]
};

// Set up enemy units for story mode
function setupStoryEnemies(stageNumber) {
  // Clear existing enemy units
  player2Units = [];
  player2Slots = [0, 1, 2];

  const difficulty = gameState.currentDifficulty;
  const isBoss = stageNumber % 5 === 0;

  let enemyPower = 1;
  switch (difficulty) {
    case 'easy':
      enemyPower = 0.99 * Math.pow(1.012, stageNumber - 1);
      break;
    case 'normal':
      enemyPower = 1.2 * Math.pow(1.018, stageNumber - 1);
      break;
    case 'hard':
      enemyPower = 1.45 * Math.pow(1.025, stageNumber - 1);
      break;
    case 'zeed':
      enemyPower = 2 * Math.pow(1.035, stageNumber - 1);
      break;
    case 'brainrot':
      enemyPower = 3.5 * Math.pow(1.045, stageNumber - 1);
      break;
  }
  if (isBoss) {
    enemyPower *= 1.54; // Boss โหดขึ้นแต่ไม่เวอร์
  }

  // Get the fixed enemy composition for this stage
  const stageIndex = stageNumber - 1;
  if (stageIndex < 0 || stageIndex >= stageEnemies[difficulty].length) {
    console.error('Invalid stage number:', stageNumber);
    return;
  }

  const enemySelection = stageEnemies[difficulty][stageIndex];

  // Create enemy units
  enemySelection.forEach((enemyType, index) => {
    const enemyChar = characters.find(c => c.type === enemyType);
    if (enemyChar) {
      // Create a powered-up version of the character
      const poweredChar = { ...enemyChar };

      // Apply regular power scaling (คูณเฉพาะ hp, attack, defense)
      poweredChar.hp = Math.floor(poweredChar.hp * (enemyPower * 0.9 + 0.1)); // More gradual
      poweredChar.attack = Math.floor(poweredChar.attack * (enemyPower * 0.9 + 0.1));
      poweredChar.defense = Math.floor(poweredChar.defense * (enemyPower * 0.9 + 0.1));
      // ไม่คูณ speed, critChance, attackSpeed

      // For boss stages, make the first character a super-sized boss
      if (isBoss && index === 0) {
        // Additional boss buffs (ปรับให้ไม่แข็งแกร่งเกินไป)
        poweredChar.hp = Math.floor(poweredChar.hp * 1.3); // เพิ่ม HP
        poweredChar.attack = Math.floor(poweredChar.attack * 1.12); // เพิ่มพลังโจมตีเล็กน้อย
        poweredChar.defense = Math.floor(poweredChar.defense * 1.12); // เพิ่มการป้องกันเล็กน้อย
        poweredChar.speed = Math.floor(poweredChar.speed * 0.95); // ช้าลงเล็กน้อย
        poweredChar.isBoss = true; // Mark as boss for visual effects
      }

      // Add the enemy unit (ส่ง poweredChar เป็น customStats)
      selectCharacter(poweredChar, 2, poweredChar);
    }
  });
}

// Start bot mode
function startBotMode() {
  if (gameState.lastBotGame) {
    const now = new Date().getTime();
    const lastGame = new Date(gameState.lastBotGame).getTime();
    const cooldownTime = gameState.botCooldownHours * 60 * 60 * 1000;

    if (now - lastGame < cooldownTime) {
      // Calculate remaining time
      const remainingMs = cooldownTime - (now - lastGame);
      const remainingHours = Math.ceil(remainingMs / (1000 * 60 * 60));

      showModal('Cooldown Active', `Bot mode is on cooldown. Please try again in ${remainingHours} hour${remainingHours > 1 ? 's' : ''}.`);
      return;
    }
  }

  gameState.currentMode = 'bot';
  showScreen('game');

  // Update game mode info
  document.getElementById('gameModeInfo').textContent = 'Bot Challenge';

  // Reset game
  resetGame();

  // Set up bot enemy units
  setupBotEnemies();
  
  // Track bot mode start
  trackGameAction('Bot Mode Started');
}

// Fixed bot enemy compositions
const botEnemyCompositions = [
  ['knight', 'archer', 'tank'],
  ['reaper', 'mage', 'tank'],
  ['ninja', 'archer', 'ghost'],
  ['murder', 'vampire', 'gunman'],
  ['knight', 'ninja', 'reaper'],
  ['murder', 'mage', 'ghost'],
  ['vampire', 'gunman', 'knight'],
  ['ninja', 'reaper', 'murder']
];

// Set up bot enemy units
function setupBotEnemies() {
  // Clear existing enemy units
  player2Units = [];
  player2Slots = [0, 1, 2];

  // Select a fixed composition based on the current date
  // This ensures the bot uses different teams on different days
  const today = new Date();
  const dayOfYear = Math.floor((today - new Date(today.getFullYear(), 0, 0)) / (1000 * 60 * 60 * 24));
  const compositionIndex = dayOfYear % botEnemyCompositions.length;
  const enemySelection = botEnemyCompositions[compositionIndex];

  // Add a boss character with a 20% chance (ลดโอกาสเจอ boss)
  const hasBoss = Math.random() < 0.2;

  // Create enemy units
  enemySelection.forEach((enemyType, index) => {
    const enemyChar = characters.find(c => c.type === enemyType);
    if (enemyChar) {
      // Create a powered-up version of the character
      const poweredChar = { ...enemyChar };

      // For boss, make the first character a super-sized boss
      if (hasBoss && index === 0) {
        // Apply boss buffs (ปรับให้ไม่แข็งแกร่งเกินไป)
        poweredChar.hp = Math.floor(poweredChar.hp * 1.4);
        poweredChar.attack = Math.floor(poweredChar.attack * 1.15);
        poweredChar.defense = Math.floor(poweredChar.defense * 1.15);
        poweredChar.speed = Math.floor(poweredChar.speed * 0.9);
        poweredChar.isBoss = true;
      }

      // Add the enemy unit
      selectCharacter(poweredChar, 2);
    }
  });
}

// Handle game result
function handleGameResult(playerWon) {
  const resultScreen = document.getElementById('resultScreen');
  const resultTitle = document.getElementById('resultTitle');
  const resultSubtitle = document.getElementById('resultSubtitle');
  const resultReward = document.getElementById('resultReward');
  const continueButton = document.getElementById('continueButton');
  const replayButton = document.getElementById('replayButton');

  resultScreen.style.display = 'flex';
  
  // Track game result
  trackGameAction('Game Ended', {
    mode: gameState.currentMode,
    result: playerWon ? 'Victory' : 'Defeat',
    stage: gameState.currentStage || null,
    difficulty: gameState.currentDifficulty || null
  });

  // Show/hide buttons based on game mode
  if (gameState.currentMode === 'custom') {
    replayButton.style.display = 'block';
    replayButton.textContent = 'Play Again';
    continueButton.textContent = 'Go Home';
  } else {
    replayButton.style.display = 'none';
    // For story mode, change continue button to "Next Stage" if player won
    if (gameState.currentMode === 'story' && playerWon) {
      continueButton.textContent = 'Next Stage';
    } else {
      continueButton.textContent = 'Continue';
    }
  }

  if (playerWon) {
    resultTitle.textContent = 'Victory!';

    let rewardCoins = 0;
    let subtitle = '';
    let shouldReward = true;

    // Determine rewards based on game mode
    if (gameState.currentMode === 'story') {
      const stageNumber = gameState.currentStage;
      const difficulty = gameState.currentDifficulty;
      const isBoss = stageNumber % 5 === 0;

      subtitle = `You completed ${difficulty} stage ${stageNumber}!`;

      // Check if stage has already been completed
      const isStageCompleted = gameState.completedStages[difficulty].includes(stageNumber);

      // Only award coins if this is the first time completing the stage
      if (!isStageCompleted) {
        // Base reward depends on difficulty and stage
        switch (difficulty) {
          case 'easy':
            rewardCoins = 30 + Math.floor(stageNumber * 2.25);
            break;
          case 'normal':
            rewardCoins = 40 + Math.floor(stageNumber * 3);
            break;
          case 'hard':
            rewardCoins = 50 + Math.floor(stageNumber * 4);
            break;
          case 'zeed':
            rewardCoins = 70 + Math.floor(stageNumber * 6);
            break;
          case 'brainrot':
            rewardCoins = 100 + Math.floor(stageNumber * 8);
            break;
        }

        // Boss stages give extra rewards
        if (isBoss) {
          rewardCoins = rewardCoins * 2;
        }
      } else {
        subtitle += " (No reward for replaying completed stages)";
        shouldReward = false;
      }

      // Mark stage as completed if not already
      if (!isStageCompleted) {
        gameState.completedStages[difficulty].push(stageNumber);
        gameState.completedStages[difficulty].sort((a, b) => a - b);

        // Save game state immediately to ensure progress is recorded
        saveGameState();

        // Update UI to reflect the new completed stage
        updateUI();

        // Recreate story stages to update the visual state
        createStoryStages();
      }
    }
    else if (gameState.currentMode === 'bot') {
      rewardCoins = 150;
      subtitle = 'You defeated the bot challenge!';
    }
    else if (gameState.currentMode === 'custom') {
      // No rewards for custom games
      rewardCoins = 0;
      subtitle = 'You won the custom game! (No rewards in custom mode)';
      shouldReward = false;
    }

    // Add coins to player's balance only if there's a reward
    if (shouldReward && rewardCoins > 0) {
      gameState.coins += rewardCoins;
      saveGameState();

      // Update UI to reflect new coin balance
      document.getElementById('coinBalance').textContent = gameState.coins;
      document.getElementById('shopCoinBalance').textContent = gameState.coins;
      document.getElementById('inGameCoinBalance').textContent = gameState.coins;

      // Update shop buttons if shop is open
      if (gameState.currentScreen === 'shop') {
        updateShopButtons();
      }

      resultSubtitle.textContent = subtitle;
      resultReward.textContent = `+${rewardCoins} Coins`;
      resultReward.style.display = 'block';
    } else {
      resultSubtitle.textContent = subtitle;
      resultReward.style.display = 'none';
    }
  }
  else {
    resultTitle.textContent = 'Defeat!';
    resultSubtitle.textContent = 'Better luck next time!';
    resultReward.style.display = 'none';
  }
}

// Admin mode for adding coins
let adminModeEnabled = false;
let adminCodeInput = '';

function checkAdminCode(key) {
  // รหัสคือ faisgodgg
  const adminCode = 'faisgodgg';

  // เพิ่มตัวอักษรที่กดเข้าไปในรหัสที่กำลังพิมพ์
  adminCodeInput += key;

  // ตัดให้เหลือเท่ากับความยาวของรหัส
  if (adminCodeInput.length > adminCode.length) {
    adminCodeInput = adminCodeInput.substring(adminCodeInput.length - adminCode.length);
  }

  // ตรวจสอบว่าตรงกับรหัสหรือไม่
  if (adminCodeInput === adminCode) {
    adminModeEnabled = true;
    localStorage.setItem('adminEnabled', 'true'); // บันทึกสถานะ admin ไว้
    showAdminPanel();
    showNotification('Admin mode activated!', 'success');
    adminCodeInput = ''; // รีเซ็ตรหัส
  }
}

function showAdminPanel() {
  // สร้างปุ่ม admin ถ้ายังไม่มี
  if (!document.getElementById('adminButton')) {
    const adminButton = document.createElement('button');
    adminButton.id = 'adminButton';
    adminButton.className = 'btn';
    adminButton.style.position = 'fixed';
    adminButton.style.bottom = '10px';
    adminButton.style.right = '10px';
    adminButton.style.zIndex = '9999';
    adminButton.style.backgroundColor = '#ff4655';
    adminButton.textContent = 'Admin';

    adminButton.addEventListener('click', () => {
      // แสดง modal สำหรับเพิ่มเงินและปลดล็อกด่าน
      showModal('Admin Panel', 'Enter amount of coins to add:', [
        {
          text: 'Add Coins',
          action: () => {
            const input = document.getElementById('adminCoinsInput');
            const amount = parseInt(input.value);
            if (!isNaN(amount)) {
              gameState.coins += amount;
              saveGameState();
              updateUI();
              showNotification(`Added ${amount} coins!`, 'success');
            }
          }
        },
        {
          text: 'Unlock Stage',
          action: () => {
            const diffSelect = document.getElementById('adminStageDiff');
            const difficulty = diffSelect.value;
            if (['easy','normal','hard','zeed','brainrot'].includes(difficulty)) {
              // Unlock all stages 1-25 for the selected difficulty
              gameState.completedStages[difficulty] = Array.from({length: 25}, (_, i) => i + 1);
              saveGameState();
              updateUI();
              createStoryStages();
              showNotification(`Unlocked all stages (1-25) for ${difficulty}!`, 'success');
            } else {
              showNotification('Invalid difficulty.', 'error');
            }
          }
        },
        {
          text: 'Cancel',
          secondary: true
        }
      ]);

      // เพิ่ม input field ใน modal
      const modalBody = document.querySelector('.game-modal-body');
      // Coin input
      const input = document.createElement('input');
      input.id = 'adminCoinsInput';
      input.type = 'number';
      input.min = '1';
      input.value = '1000';
      input.style.width = '100%';
      input.style.padding = '8px';
      input.style.marginTop = '10px';
      modalBody.appendChild(input);

      // Divider
      const divider = document.createElement('hr');
      divider.style.margin = '16px 0';
      modalBody.appendChild(divider);

      // Difficulty select
      const diffLabel = document.createElement('label');
      diffLabel.textContent = 'Select Difficulty:';
      diffLabel.style.display = 'block';
      diffLabel.style.marginTop = '8px';
      modalBody.appendChild(diffLabel);
      const diffSelect = document.createElement('select');
      diffSelect.id = 'adminStageDiff';
      diffSelect.style.width = '100%';
      diffSelect.style.padding = '8px';
      diffSelect.style.marginTop = '4px';
      ['easy','normal','hard','zeed','brainrot'].forEach(diff => {
        const opt = document.createElement('option');
        opt.value = diff;
        opt.textContent = diff.charAt(0).toUpperCase() + diff.slice(1);
        diffSelect.appendChild(opt);
      });
      modalBody.appendChild(diffSelect);
    });

    document.body.appendChild(adminButton);
  }
}

// Set up event listeners
function setupEventListeners() {
  // Listen for key presses for admin mode
  document.addEventListener('keypress', (e) => {
    checkAdminCode(e.key.toLowerCase());
  });

  // ตรวจสอบว่ามีการพิมพ์รหัส admin ไว้แล้วหรือไม่
  if (localStorage.getItem('adminEnabled') === 'true') {
    adminModeEnabled = true;
    showAdminPanel();
  }

  // Home screen buttons
  document.getElementById('storyModeButton').addEventListener('click', () => {
    gameState.currentScreen = 'story';
    updateUI();
    createStoryStages(); // Ensure real-time update when entering story mode
    setActiveDifficultyTab();
  });

  document.getElementById('customModeButton').addEventListener('click', () => {
    gameState.currentMode = 'custom';
    showScreen('game');
    document.getElementById('gameModeInfo').textContent = 'Custom Mode';
    resetGame();
    
    // Track custom mode start
    trackGameAction('Custom Mode Started');
  });

  document.getElementById('botModeButton').addEventListener('click', startBotMode);

  document.getElementById('openShopButton').addEventListener('click', () => {
    showScreen('shop');
  });

  document.getElementById('openSettingsButton').addEventListener('click', () => {
    showScreen('settings');
  });

  document.getElementById('openUpgradeButton').addEventListener('click', () => {
    showScreen('upgrade');
  });

  document.getElementById('openItemsButton').addEventListener('click', () => {
    showScreen('items');
  });

  document.getElementById('openLogsButton').addEventListener('click', () => {
    showScreen('logs');
  });

  document.getElementById('openEquipButton').addEventListener('click', () => {
    showScreen('equip');
  });

  // Shop tabs
  const shopTabs = document.querySelectorAll('.shop-tab');
  shopTabs.forEach(tab => {
    tab.addEventListener('click', () => {
      // Remove active class from all tabs and tab contents
      shopTabs.forEach(t => t.classList.remove('active'));
      document.querySelectorAll('.shop-tab-content').forEach(content => {
        content.classList.remove('active');
      });

      // Add active class to clicked tab
      tab.classList.add('active');

      // Show corresponding tab content
      const tabName = tab.dataset.tab;
      document.getElementById(`${tabName}Tab`).classList.add('active');
    });
  });

  // Shop screen
  document.getElementById('closeShopButton').addEventListener('click', () => {
    gameState.currentScreen = 'home';
    saveGameState();
    showScreen('home');
  });

  // Story screen
  document.getElementById('storyBackButton').addEventListener('click', () => {
    gameState.currentScreen = 'home';
    saveGameState();
    showScreen('home');
  });

  // Difficulty tabs
  const difficultyTabs = document.querySelectorAll('.difficulty-tab');
  difficultyTabs.forEach(tab => {
    tab.addEventListener('click', () => {
      // Skip if tab is locked
      if (tab.classList.contains('locked')) {
        return;
      }

      difficultyTabs.forEach(t => t.classList.remove('active'));
      tab.classList.add('active');
      gameState.currentDifficulty = tab.dataset.difficulty;
      saveGameState();
      createStoryStages();
      setActiveDifficultyTab(); // Ensure active tab is always correct
    });
  });

  // Settings screen
  document.getElementById('closeSettingsButton').addEventListener('click', () => {
    gameState.currentScreen = 'home';
    saveGameState();
    showScreen('home');
  });

  // Items screen
  document.getElementById('closeItemsButton').addEventListener('click', () => {
    gameState.currentScreen = 'home';
    saveGameState();
    showScreen('home');
  });

  // Logs screen
  document.getElementById('closeLogsButton').addEventListener('click', () => {
    gameState.currentScreen = 'home';
    saveGameState();
    showScreen('home');
  });

  document.getElementById('exportDataButton').addEventListener('click', () => {
    const dataField = document.getElementById('gameDataField');
    dataField.value = exportGameData();
    dataField.select();
    document.execCommand('copy');
    showNotification('Game data copied to clipboard!', 'success');
  });

  document.getElementById('importDataButton').addEventListener('click', () => {
    const dataField = document.getElementById('gameDataField');
    if (dataField.value.trim() === '') {
      showNotification('Please paste game data to import.', 'warning');
      return;
    }

    if (importGameData(dataField.value.trim())) {
      showNotification('Game data imported successfully!', 'success');
      dataField.value = '';
    } else {
      showNotification('Failed to import game data. Please check the format.', 'error');
    }
  });

  // Add/keep forceFixButton logic if not already present
  const forceFixBtn = document.getElementById('forceFixButton');
  if (forceFixBtn) {
    forceFixBtn.onclick = function() {
      if (confirm('This will delete ALL your game data and refresh the page. Continue?')) {
        localStorage.clear();
        location.reload();
      }
    };
  }

  // Game screen
  document.getElementById('backToHomeButton').addEventListener('click', () => {
    showModal('Return to Home', 'Are you sure you want to return to the home screen? Your current game progress will be lost.', [
      {
        text: 'Return',
        action: () => {
          // If this is a bot mode game that hasn't started yet, don't count it as played
          if (gameState.currentMode === 'bot' && !battleStarted) {
            // Reset the current mode
            gameState.currentMode = 'custom';
          }

          // If battle is in progress, stop it and clear all units
          if (battleStarted) {
            battleStarted = false;
            gameEnded = true;

            // Clear all units from the battlefield
            const battlefield = document.getElementById('battlefield');
            const units = battlefield.querySelectorAll('.unit');
            units.forEach(unit => unit.remove());

            // Reset unit arrays
            player1Units = [];
            player2Units = [];
            player1Clones = [];
            player2Clones = [];
            player1Slots = [0, 1, 2];
            player2Slots = [0, 1, 2];
          }

          showScreen('home');
        }
      },
      {
        text: 'Stay',
        secondary: true
      }
    ]);
  });

  // Result screen
  document.getElementById('continueButton').addEventListener('click', () => {
    document.getElementById('resultScreen').style.display = 'none';

    // If in story mode and player won, go to next stage
    if (gameState.currentMode === 'story' && document.getElementById('resultTitle').textContent === 'Victory!') {
      const nextStage = gameState.currentStage + 1;

      // Check if next stage exists (max 25 stages)
      if (nextStage <= 25) {
        // Update story stages to reflect any changes before starting next stage
        createStoryStages();

        // Start the next stage
        startStoryStage(nextStage);
      } else {
        // If completed all stages, go back to story selection
        showScreen('story');

        // Update UI and recreate story stages to reflect completion
        updateUI();
        createStoryStages();
      }
    } else {
      // For other modes or defeat, go back to home
      if (gameState.currentMode === 'story') {
        showScreen('story');
        updateUI();
        createStoryStages();
      } else {
        showScreen('home');
        updateUI();
      }
    }
  });

  document.getElementById('replayButton').addEventListener('click', () => {
    document.getElementById('resultScreen').style.display = 'none';
    resetGame();

    // If in story mode, set up enemies again for the same stage
    if (gameState.currentMode === 'story') {
      setupStoryEnemies(gameState.currentStage);
    }
    // If in bot mode, set up bot enemies again
    else if (gameState.currentMode === 'bot') {
      setupBotEnemies();
    }
  });

  document.getElementById('infiniteModeButton').addEventListener('click', startInfiniteMode);

  document.getElementById('closeEquipButton').addEventListener('click', () => {
    gameState.currentScreen = 'home';
    saveGameState();
    showScreen('home');
  });
}

function createCharacterSelection() {
  const player1Chars = document.getElementById('player1-characters');
  const player2Chars = document.getElementById('player2-characters');

  player1Chars.innerHTML = '';
  player2Chars.innerHTML = '';

  // --- Add slot info and buy button for Player 1 ---
  const slotInfoDiv = document.createElement('div');
  slotInfoDiv.className = 'slot-info';
  slotInfoDiv.style.marginBottom = '8px';
  slotInfoDiv.textContent = `Unit Slots: ${gameState.player1SlotCount}/6`;
  player1Chars.appendChild(slotInfoDiv);

  if (gameState.player1SlotCount < 6) {
    const buySlotBtn = document.createElement('button');
    buySlotBtn.className = 'buy-slot-button';
    buySlotBtn.textContent = `Add Slot (500 coins)`;
    buySlotBtn.style.marginBottom = '8px';
    buySlotBtn.disabled = gameState.coins < 500;
    if (gameState.coins >= 500) {
      buySlotBtn.classList.add('can-afford');
    }
    buySlotBtn.onclick = () => {
      if (gameState.coins < 500) {
        showNotification('Not enough coins to buy a slot!', 'error');
        return;
      }
      if (gameState.player1SlotCount >= 6) {
        showNotification('Maximum slots reached!', 'warning');
        return;
      }
      gameState.coins -= 500;
      gameState.player1SlotCount += 1;
      player1Slots.push(player1Slots.length); // Add new slot index
      saveGameState();
      updateUI();
      createCharacterSelection();
      showNotification('You have unlocked a new unit slot!', 'success');
    };
    player1Chars.appendChild(buySlotBtn);
  }

  // --- End slot info/button ---

  characters.forEach(char => {
    // Skip dummy character
    if (char.type === 'Dummy') return;

    // Player 1 characters (only show unlocked characters)
    const isUnlocked = gameState.unlockedCharacters.includes(char.type);
    const charDiv1 = document.createElement('div');
    charDiv1.className = isUnlocked ? 'character' : 'character locked';
    charDiv1.innerHTML = (char.type === 'tungtung' ? `<img src="brainrot1.png" alt="tungtung" style="width:28px;height:28px;"><br>` : `${char.name}<br>`) + `${char.type}`;

    if (isUnlocked) {
      charDiv1.onclick = () => selectCharacter(char, 1);
    }

    player1Chars.appendChild(charDiv1);

    // Player 2 characters (in custom mode, only show unlocked characters)
    const charDiv2 = document.createElement('div');
    const isCustomMode = gameState.currentMode === 'custom';
    const showForPlayer2 = isCustomMode ? isUnlocked : true;

    charDiv2.className = showForPlayer2 ? 'character' : 'character locked';
    charDiv2.innerHTML = (char.type === 'tungtung' ? `<img src="brainrot1.png" alt="tungtung" style="width:28px;height:28px;"><br>` : `${char.name}<br>`) + `${char.type}`;

    // In custom mode, player can select characters for both sides
    // In story/bot mode, player cannot select enemy characters
    if (isCustomMode && showForPlayer2) {
      charDiv2.onclick = () => selectCharacter(char, 2);
    }

    player2Chars.appendChild(charDiv2);
  });
}

function selectCharacter(char, player, customStats = null) {
  if (battleStarted || gameEnded) return;

  const units = player === 1 ? player1Units : player2Units;
  const slots = player === 1 ? player1Slots : player2Slots;

  if (slots.length > 0) {
    const slot = slots.shift();
    // ใช้ customStats ถ้ามี (เช่น story mode ฝั่งศัตรู)
    let unit;
    if (customStats) {
      unit = { ...customStats };
    } else {
      // Story mode ฝั่ง player2 ไม่ได้อัพเกรด
      unit = (gameState.currentMode === 'story' && player === 2)
        ? getCharacterWithUpgrades(char.type, true)
        : getCharacterWithUpgrades(char.type);
    }

    // If this is player 1's first character, update the character header
    if (player === 1 && player1Units.filter(u => u).length === 0) {
      gameState.selectedCharacter = char.type;
      updateCharacterHeader(unit);
    }

    // Apply item effects to the unit (only for player 1 units)
    if (player === 1) {
      unit = applyItemEffects(unit);
    }
    
    // Initialize additional properties
    unit.lifesteal = unit.lifesteal || (unit.type === 'vampire' ? 0.15 : 0); // Only vampires have lifesteal by default
    
    // Set path เฉพาะถ้าไม่ใช่ story mode + player 2
    if (!(gameState.currentMode === 'story' && player === 2) && !customStats) {
      if (char.type === 'berserk') {
        const upgradeData = gameState.characterUpgrades['berserk'];
        if (upgradeData) {
          unit.path = upgradeData.path;
        }
      }
      if (char.type === 'archer') {
        const upgradeData = gameState.characterUpgrades['archer'];
        if (upgradeData) {
          unit.path = upgradeData.path;
        }
      }
      if (char.type === 'tank') {
        const upgradeData = gameState.characterUpgrades['tank'];
        if (upgradeData) {
          unit.path = upgradeData.path;
        }
      }
      if (char.type === 'mage') {
        const upgradeData = gameState.characterUpgrades['mage'];
        if (upgradeData) {
          unit.path = upgradeData.path;
        }
      }
      if (char.type === 'vampire') {
        const upgradeData = gameState.characterUpgrades['vampire'];
        if (upgradeData) {
          unit.path = upgradeData.path;
        }
      }
      if (char.type === 'gunman') {
        const upgradeData = gameState.characterUpgrades['gunman'];
        if (upgradeData) {
          unit.path = upgradeData.path;
        }
      }
      if (char.type === 'knight') {
        const upgradeData = gameState.characterUpgrades['knight'];
        if (upgradeData) {
          unit.path = upgradeData.path;
        }
      }
      if (char.type === 'ninja') {
        const upgradeData = gameState.characterUpgrades['ninja'];
        if (upgradeData) {
          unit.path = upgradeData.path;
        }
      }
    }



    if (char.type === 'archer' && !customStats) {
      const upgradeData = gameState.characterUpgrades['archer'];
      if (upgradeData) {
        unit.path = upgradeData.path; // Add path to the unit object
      }
    }

    if (char.type === 'tank' && !customStats) {
      const upgradeData = gameState.characterUpgrades['tank'];
      if (upgradeData) {
        unit.path = upgradeData.path; // Add path to the unit object
      }
    }

    if (char.type === 'mage' && !customStats) {
      const upgradeData = gameState.characterUpgrades['mage'];
      if (upgradeData) {
        unit.path = upgradeData.path; // Add path to the unit object
      }
    }

    if (char.type === 'vampire' && !customStats) {
      const upgradeData = gameState.characterUpgrades['vampire'];
      if (upgradeData) {
        unit.path = upgradeData.path;
      }
    }

    if (char.type === 'gunman' && !customStats) {
      const upgradeData = gameState.characterUpgrades['gunman'];
      if (upgradeData) {
        unit.path = upgradeData.path;
      }
    }

    if (char.type === 'knight' && !customStats) {
      const upgradeData = gameState.characterUpgrades['knight'];
      if (upgradeData) {
        unit.path = upgradeData.path;
      }
    }

    if (char.type === 'ninja' && !customStats) {
      const upgradeData = gameState.characterUpgrades['ninja'];
      if (upgradeData) {
        unit.path = upgradeData.path; // Add path to the unit object
      }
    }
    if (char.type === 'cyborg' && !customStats) {
      const upgradeData = gameState.characterUpgrades['cyborg'];
      if (upgradeData) {
        unit.path = upgradeData.path; // Add path to the unit object
      }
    }

    if (unit.type === 'ghost' && !unit.form) unit.form = 'human';
    unit.id = `p${player}-${slot}`;
    unit.health = unit.hp;
    unit.maxHealth = unit.hp;
    unit.player = player;
    unit.x = player === 1 ? 50 : 510;
    unit.y = 150 + slot * 60;
    unit.targetX = unit.x;
    unit.targetY = unit.y;
    unit.lastAttack = 0;
    unit.attackCooldown = 1000 / unit.attackSpeed;
    unit.isImmune = false;

    unit.skillActive = false;
    unit.lastSkillTime = 0;
    unit.originalAttack = unit.attack;
    unit.originalDefense = unit.defense;
    unit.originalSpeed = unit.speed;
    unit.originalAttackSpeed = unit.attackSpeed;

    // Reset ninja vanish state for new battle
    if (char.type === 'ninja') {
      unit.hasVanished = false;
    }

    // Reset teleport thresholds for new battle
    unit.passedThresholds = [];
    unit.teleportCount = 0;

    // Initialize berserk attack stack
    if (char.type === 'berserk') {
      unit.attackStack = 0;
      unit.nextAttackDrain = false;
    }

    // Initialize additional properties
    unit.lifesteal = unit.lifesteal || 0;

    // Debug log for lifesteal initialization
    if (unit.type === 'vampire') {
      console.log(`Initialized vampire with lifesteal: ${unit.lifesteal}`);
    }

    // For Reaper, apply reaperBonus to deathCheatChance if it exists
    if (unit.type === 'reaper' && unit.reaperBonus) {
      unit.deathCheatChance += unit.reaperBonus;
    }

    // Add fire aura visual effect if character has fire shield
    if (unit.hasFireAura) {
      setTimeout(() => {
        const unitEl = document.getElementById(unit.id);
        if (unitEl) {
          const fireAura = document.createElement('div');
          fireAura.className = 'fire-aura-permanent';
          fireAura.style.position = 'absolute';
          fireAura.style.left = '-25px';
          fireAura.style.top = '-25px';
          fireAura.style.width = '90px';
          fireAura.style.height = '90px';
          fireAura.style.background = 'radial-gradient(circle, rgba(255,100,0,0.4) 0%, rgba(255,50,0,0.2) 50%, rgba(255,0,0,0.1) 100%)';
          fireAura.style.borderRadius = '50%';
          fireAura.style.zIndex = '5';
          fireAura.style.pointerEvents = 'none';
          fireAura.style.animation = 'fireAuraGlow 3s ease-in-out infinite alternate';
          unitEl.appendChild(fireAura);
          unit.fireAuraElement = fireAura;
        }
      }, 100);
    }

    switch (char.type) {
      case 'berserk':
        unit.specialCooldown = 15000;
        unit.skillDuration = 5000;
        break;
      case 'archer':
        unit.specialCooldown = 12000;
        unit.skillDuration = 5000;
        break;
      case 'tank':
        unit.specialCooldown = 15000;
        unit.skillDuration = 3000;
        break;
      case 'mage':
        unit.specialCooldown = 12000;
        break;
      case 'knight':
        unit.specialCooldown = 12000;
        unit.skillDuration = 4000;
        break;
      case 'ninja':
        unit.specialCooldown = 12000;
        unit.skillDuration = 5000;
        unit.windWalkActive = false;
        break;
      case 'murder':
        unit.specialCooldown = 12000;
        unit.skillDuration = 6000;
        unit.killCount = 0;
        break;
      case 'vampire':
        unit.specialCooldown = 12000;
        unit.skillDuration = 2000;
        break;
      case 'gunman':
        unit.specialCooldown = 10000;
        unit.skillDuration = 5000;
        break;
      case 'reaper':
        unit.specialCooldown = 10000;
        unit.skillDuration = 5000;
        break;
      case 'ghost':
        unit.specialCooldown = 10000;
        unit.skillDuration = 5000;
        break;
      case 'cyborg':
        unit.specialCooldown = 12000;
        unit.skillDuration = 4000;
        unit.isRecharging = false;
        unit.rechargeAttempted = false; // Initialize recharge attempt flag
        unit.cyborgAttackCount = 0;
        unit.rechargeChance = 60; // Initialize recharge chance at 60%
        break;
      case 'tungtung':
        unit.specialCooldown = 12000;
        unit.skillDuration = 5000;
        break;
      default:
        unit.specialCooldown = 10000;
        unit.skillDuration = 5000;
    }

    units[slot] = unit;

    const battlefield = document.getElementById('battlefield');
    const unitElement = document.createElement('div');
    unitElement.className = `unit player${player}`;

    // Add boss class if this is a boss unit
    if (char.isBoss) {
      unitElement.classList.add('boss');
    }

    unitElement.innerHTML = `
      ${(char.type === 'tungtung') ? `<img src="brainrot1.png" alt="tungtung" style="width:28px;height:28px;">` : char.name}
      <div class="health-bar">
        <div class="health-bar-fill" style="width: 100%"></div>
      </div>
    `;
    unitElement.id = unit.id;
    unitElement.style.left = `${unit.x}px`;
    unitElement.style.top = `${unit.y}px`;
    unitElement.addEventListener('click', (e) => {
      if (!battleStarted) {
        // เฉพาะ custom mode หรือ unit ฝั่ง player 1 เท่านั้นที่ลบได้
        if (
          (gameState.currentMode === 'custom') ||
          (unit.player === 1)
        ) {
          removeUnit(unit.id);
          e.stopPropagation();
        }
      }
    });

    battlefield.appendChild(unitElement);
    updateStatus();

    // เพิ่ม uniqueId ให้กับยูนิต
    unit.uniqueId = nextUnitUniqueId++;
  }
}

function handleUnitDeath(attacker, target) {
  // Check for respawn mechanics first
  if (target.health <= 0) {
    // Check for instant revive from โชเล่ห์ (Soul Pendant)
    if (target.instantReviveCount && target.instantReviveCount > 0) {
      target.instantReviveCount--; // Use one revive charge
      target.health = target.maxHealth; // Revive with full HP

      const targetEl = document.getElementById(target.id);
      if (targetEl) {
        const healthBar = targetEl.querySelector('.health-bar-fill');
        healthBar.style.width = '100%';
        targetEl.classList.add('revived');
        targetEl.style.filter = 'brightness(1.5) drop-shadow(0 0 20px #00ffff)';
        showDamageText(target.x, target.y, 'โชเลย์ Revival!', 'heal');
        setTimeout(() => {
          targetEl.classList.remove('revived');
          targetEl.style.filter = '';
        }, 1000);
      }
      return; // Don't remove the unit
    }

    // Dark Knight revival
    if (target.type === 'knight' && target.path === 'path2') {
      const reviveRoll = Math.random() * 100;
      if (reviveRoll <= 40) { // 40% chance
        target.health = Math.floor(target.maxHealth * 0.3); // Revive with 30% HP
        target.attack = Math.floor(target.attack * 1.1); // Permanent attack boost
        target.defense = Math.floor(target.defense * 1.1); // Permanent defense boost

        const targetEl = document.getElementById(target.id);
        if (targetEl) {
          const healthBar = targetEl.querySelector('.health-bar-fill');
          healthBar.style.width = `${(target.health / target.maxHealth) * 100}%`;
          targetEl.classList.add('revived');
          targetEl.style.filter = 'brightness(1.5) drop-shadow(0 0 20px #000)';
          showDamageText(target.x, target.y, 'Dark Revival!', 'heal');
          setTimeout(() => {
            targetEl.classList.remove('revived');
            targetEl.style.filter = '';
          }, 1000);
        }
        return; // Don't remove the unit
      }
    }

    if (target.type === 'ghost' && target.form === 'human') {
      respawnGhost(target);
      const targetEl = document.getElementById(target.id);
      if (targetEl) {
        const healthBar = targetEl.querySelector('.health-bar-fill');
        healthBar.style.width = '100%';
      }
      return; // Don't remove the unit
    } else if (target.type === 'reaper') {
      // Set default multipliers for reaper
      const safeAttackMultiplier = 1.0;
      const safeDefenseMultiplier = 1.0;
      const safeSpeedMultiplier = 1.0;

      // Apply reaperBonus to death cheat chance if present
      let cheatChance = target.deathCheatChance;
      if (target.reaperBonus) {
        cheatChance += target.reaperBonus * 10; // Increase chance by 10% per 0.1 reaperBonus
      }

      const cheatRoll = Math.random() * 100;
      if (cheatRoll <= cheatChance) {
        if (target.deathCheatChance === 100) target.deathCheatChance = 30;

        // Apply revival stats
        target.maxHealth = Math.floor(target.maxHealth * 1.05);
        target.health = Math.floor(target.maxHealth * 0.6);
        target.defense += 0.5;
        target.attack += 0.5;
        target.attackSpeed += 0.5;
        target.speed += 1;
        target.deathCheatChance += 5;
        target.attackCooldown = 1000 / target.attackSpeed;
        target.reviveCount = (target.reviveCount || 0) + 1;

        const targetEl = document.getElementById(target.id);
        if (targetEl) {
          const healthBar = targetEl.querySelector('.health-bar-fill');
          healthBar.style.width = `${(target.health / target.maxHealth) * 100}%`;
          targetEl.classList.add('revived');
          showDamageText(target.x, target.y, 'Revived!', 'heal');
          setTimeout(() => targetEl.classList.remove('revived'), 1000);
        }
        return; // Don't remove the unit
      }
    } else if (target.type === 'cyborg') {
      // Special cyborg vs cyborg mechanic: if attacker is also cyborg, target dies instantly with 0% recharge chance
      if (attacker && attacker.type === 'cyborg') {
        // Cyborg vs cyborg: instant death, no recharge
        target.rechargeAttempted = true; // Prevent any recharge attempt
        showDamageText(target.x, target.y, 'Cyborg Override!', 'crit');
        
        // Visual effect for cyborg override
        const targetEl = document.getElementById(target.id);
        if (targetEl) {
          targetEl.style.filter = 'brightness(2) hue-rotate(180deg)';
          targetEl.style.boxShadow = '0 0 30px #ff0000';
          setTimeout(() => {
            targetEl.style.filter = '';
            targetEl.style.boxShadow = '';
          }, 1000);
        }
      } else {
        // Normal cyborg recharge mechanic: dynamic chance to recharge when dead
        // Prevent multiple recharge attempts
        if (target.isRecharging || target.rechargeAttempted) {
          return; // Already recharging or attempted recharge
        }
        
        const rechargeRoll = Math.random() * 100;
        if (rechargeRoll <= target.rechargeChance) {
          // Start recharge process
          target.isRecharging = true;
          target.rechargeAttempted = true; // Mark that recharge has been attempted
          
          // Recharge chance remains at 60% for every death
          target.isImmune = true; // Make invulnerable during recharge
          target.health = 1; // Set to 1 HP to prevent death
          target.defense = target.defense * 1.1
          target.rechargeStartTime = Date.now();
          target.rechargeDuration = 4000; // 4 seconds
          target.rechargeTargetHealth = target.maxHealth; // Target full health
          
          // Visual effect: make cyborg gray
          const targetEl = document.getElementById(target.id);
          if (targetEl) {
            target.range -= 1;
            targetEl.style.filter = 'grayscale(100%) brightness(0.7)';
            targetEl.style.opacity = '0.8';
            
            // Add recharge effect
            const rechargeEffect = document.createElement('div');
            rechargeEffect.className = 'cyborg-recharge-effect';
            rechargeEffect.textContent = '🔋';
            rechargeEffect.style.position = 'absolute';
            rechargeEffect.style.top = '-20px';
            rechargeEffect.style.left = '50%';
            rechargeEffect.style.transform = 'translateX(-50%)';
            rechargeEffect.style.fontSize = '16px';
            rechargeEffect.style.color = '#00eaff';
            rechargeEffect.style.textShadow = '0 0 8px #00eaff';
            rechargeEffect.style.animation = 'cyborgRechargePulse 1s infinite alternate';
            targetEl.appendChild(rechargeEffect);
            
            // Store reference to remove later
            target.rechargeEffectElement = rechargeEffect;
          }
          
          showDamageText(target.x, target.y, `Recharging...`, 'heal');
          
          // Start health regeneration over 4 seconds
          const regenerateHealth = () => {
            if (!target.isRecharging) return;
            
            const elapsed = Date.now() - target.rechargeStartTime;
            const progress = Math.min(elapsed / target.rechargeDuration, 1);
            
            // Calculate health based on progress (start from 1, end at maxHealth)
            const newHealth = Math.floor(1 + (target.rechargeTargetHealth - 1) * progress);
            target.health = newHealth;
            
            const targetEl = document.getElementById(target.id);
            if (targetEl) {
              const healthBar = targetEl.querySelector('.health-bar-fill');
              healthBar.style.width = `${(target.health / target.maxHealth) * 100}%`;
            }
            
            // Continue regeneration if not complete
            if (progress < 1) {
              setTimeout(regenerateHealth, 100); // Update every 100ms for smooth animation
            } else {
              // Recharge complete
              target.isRecharging = false;
              target.rechargeAttempted = false; // Reset recharge attempt flag
              target.isImmune = false;
              target.health = target.maxHealth;
              target.range += 1;
              
              const targetEl = document.getElementById(target.id);
              if (targetEl) {
                targetEl.style.filter = '';
                targetEl.style.opacity = '';
                if (target.rechargeEffectElement) {
                  target.rechargeEffectElement.remove();
                  target.rechargeEffectElement = null;
                }
                targetEl.classList.add('revived');
                setTimeout(() => targetEl.classList.remove('revived'), 1000);
              }
              
              showDamageText(target.x, target.y, 'Recharged!', 'heal');
            }
          };
          
          // Start regeneration
          setTimeout(regenerateHealth, 100);
          
          return; // Don't remove the unit
        } else {
          // Failed recharge: cyborg dies permanently
          target.rechargeAttempted = false; // Reset flag even on permanent death
        }
      }
    }
  }

  // If no respawn mechanics triggered, remove the unit normally
  target.health = 0;
  removeUnit(target.id);
  
  // Handle special effects for the attacker
  if (attacker && attacker.type === 'reaper') soulHarvest(attacker, target);
  if (attacker && attacker.type === 'murder') {
    attacker.killCount = (attacker.killCount || 0) + 1;
    // Apply kill bonuses
    attacker.originalAttack += 1.2;
    attacker.originalSpeed += 0.5;
    attacker.critChance += 2;
    attacker.originalDefense += 0.3;

    if (!attacker.skillActive) {
      attacker.attack = attacker.originalAttack;
      attacker.speed = attacker.originalSpeed;
      attacker.defense = attacker.originalDefense;
    }
  }
  // --- Vampire path2 AoE explosion on kill ---
  if (attacker && attacker.type === 'vampire' && attacker.path === 'path2' && !target._multiKillEffectFired) {
    target._multiKillEffectFired = true;
    const aoeRadius = 100;
    const battlefield = document.getElementById('battlefield');
    const allUnits = attacker.player === 1 ? player2Units : player1Units;
    const attackerEl = document.getElementById(attacker.id);
    allUnits.forEach(enemy => {
      if (enemy && enemy.health > 0 && enemy.id !== target.id) {
        const dist = getDistance(target.x, target.y, enemy.x, enemy.y);
        if (dist <= aoeRadius) {
          const aoeDmg = Math.floor(enemy.maxHealth * 0.1);
          enemy.health = Math.max(0, enemy.health - aoeDmg);
          const enemyEl = document.getElementById(enemy.id);
          if (enemyEl) {
            const healthBar = enemyEl.querySelector('.health-bar-fill');
            healthBar.style.width = `${(enemy.health / enemy.maxHealth) * 100}%`;
            createHitEffect(enemy.id);
          }
          showDamageText(enemy.x, enemy.y, aoeDmg, 'crit');
          // Heal vampire for 20% of that enemy's HP
          const heal = Math.floor(enemy.maxHealth * 0.2);
          attacker.health = Math.min(attacker.maxHealth, attacker.health + heal);
          if (attackerEl) {
            const healthBar = attackerEl.querySelector('.health-bar-fill');
            healthBar.style.width = `${(attacker.health / attacker.maxHealth) * 100}%`;
          }
          showDamageText(attacker.x, attacker.y, heal, 'heal');
        }
      }
    });
  }
}

function removeUnit(unitId) {
  // Find the unit in player1Units
  const idx1 = player1Units.findIndex(u => u && u.id === unitId);
  if (idx1 > -1) {
    const unit = player1Units[idx1];
    // Clean up fire aura element if exists
    if (unit && unit.fireAuraElement) {
      unit.fireAuraElement.remove();
    }
    // Clean up path2 recharge interval if exists
    if (unit && unit.path2RechargeInterval) {
      clearInterval(unit.path2RechargeInterval);
      unit.path2RechargeInterval = null;
    }
    player1Units[idx1] = null;
    player1Slots.push(idx1);
    player1Slots.sort((a, b) => a - b);
  }

  // Find the unit in player2Units
  const idx2 = player2Units.findIndex(u => u && u.id === unitId);
  if (idx2 > -1) {
    const unit = player2Units[idx2];
    // Clean up fire aura element if exists
    if (unit && unit.fireAuraElement) {
      unit.fireAuraElement.remove();
    }
    // Clean up path2 recharge interval if exists
    if (unit && unit.path2RechargeInterval) {
      clearInterval(unit.path2RechargeInterval);
      unit.path2RechargeInterval = null;
    }
    player2Units[idx2] = null;
    player2Slots.push(idx2);
    player2Slots.sort((a, b) => a - b);
  }

  // Make sure the unit is physically removed from the DOM
  const el = document.getElementById(unitId);
  if (el) {
    el.remove();
    // (Removed battlefield.style.display hack)
  }

  updateStatus();
}

function updateStatus() {
  if (battleStarted) return;

  const status = document.getElementById('status');
  const startButton = document.getElementById('startButton');
  const p1Count = player1Units.filter(u => u).length;
  const p2Count = player2Units.filter(u => u).length;

  if (p1Count >=2 && p2Count >=2) {
    status.textContent = 'All units placed! Ready for battle!';
    startButton.style.display = 'block';
  } else {
    status.textContent = `Player 1: ${p1Count}/${gameState.player1SlotCount} units | Player 2: ${p2Count}/3 units`;
    startButton.style.display = 'none';
  }
}

function showDamageText(x, y, damage, type = 'normal') {
const battlefield = document.getElementById('battlefield');
const text = document.createElement('div');
text.className = `damage-text ${type}`;

let displayText;
if (type === 'miss') {
displayText = 'Miss!'; // ไม่ต้องใช้ damage เพราะเป็นการโจมตีที่พลาด
} else if (type === 'heal') {
if (typeof damage === 'number') {
  displayText = '+' + damage.toFixed(1); // แสดงการรักษาด้วยเครื่องหมาย + และจำกัดทศนิยม 1 ตำแหน่ง
} else {
  displayText = damage; // กรณีที่ damage เป็น string (เช่น "Recharging...")
}
} else {
// สำหรับประเภท 'normal', 'crit', 'headshot'
if (typeof damage === 'number') {
  displayText = '-' + damage.toFixed(1); // แสดง damage ปกติด้วยเครื่องหมาย - และจำกัดทศนิยม 1 ตำแหน่ง
} else {
  displayText = damage; // กรณีที่ damage เป็น string (เช่น "Immune", "Evaded!")
}
if (type === 'crit') {
  displayText = `Critical! ${displayText}`; // เพิ่มข้อความ Critical! สำหรับการโจมตีคริติคอล
} else if (type === 'headshot') {
  displayText = `Head Shot! ${displayText}`; // เพิ่มข้อความ Head Shot! สำหรับการโจมตีหัว
} else if (type === 'counter') {
  displayText = `Counter! ${displayText}`; // เพิ่มข้อความ Counter! สำหรับการสะท้อนความเสียหาย
}
}

text.textContent = displayText;
text.style.left = `${x}px`;
text.style.top = `${y}px`;
if (type === 'counter') {
  text.style.color = '#FFD600'; // สีเหลืองสำหรับ counter
  text.style.fontWeight = 'bold';
  text.style.textShadow = '0 0 8px #FFD600, 0 0 2px #000';
}
battlefield.appendChild(text);
setTimeout(() => text.remove(), 1000); // ลบข้อความหลังจาก 1 วินาที
}

// สร้างฟังก์ชันใหม่สำหรับเอฟเฟกต์กระพริบเมื่อโดนโจมตี
function createHitEffect(targetId, isCounterEffect = false) {
const targetEl = document.getElementById(targetId);
if (!targetEl) return;

// เพิ่มเอฟเฟกต์กระพริบเท่านั้น ไม่มีการสั่น
let flashCount = 0;
const maxFlashes = 3;
const flashInterval = setInterval(() => {
if (flashCount >= maxFlashes) {
  clearInterval(flashInterval);
  targetEl.style.filter = '';
  return;
}

// สลับระหว่างสว่างและปกติ
if (flashCount % 2 === 0) {
  if (isCounterEffect) {
    // สีเหลืองสำหรับเอฟเฟกต์สะท้อน
    targetEl.style.filter = 'brightness(2.5) contrast(2.2) sepia(1) saturate(8) hue-rotate(20deg)';
  } else {
    // สีปกติสำหรับการโดนโจมตีทั่วไป
    targetEl.style.filter = 'brightness(2.5) contrast(1.8)';
  }
} else {
  targetEl.style.filter = '';
}

flashCount++;
}, 100);
}

function findNearestTank(unit, allies) {
  return allies
    .filter(ally => ally && ally.type === 'tank' && ally.health > 0)
    .reduce((closest, current) => {
      const distToCurrent = getDistance(unit.x, unit.y, current.x, current.y);
      const distToClosest = closest ? getDistance(unit.x, unit.y, closest.x, closest.y) : Infinity;
      return distToCurrent < distToClosest ? current : closest;
    }, null);
}

function findTarget(unit, enemies) {
  const livingEnemies = enemies.filter(enemy => enemy && enemy.health > 0 && !enemy.isRecharging && !enemy.isImmune);

  // Prioritize taunting clones
  const tauntingEnemies = livingEnemies.filter(enemy => enemy.isTaunting);

  if (tauntingEnemies.length > 0) {
    // Target the nearest taunting enemy
    return tauntingEnemies.reduce((closest, current) => {
      const distToCurrent = getDistance(unit.x, unit.y, current.x, current.y);
      const distToClosest = closest ? getDistance(unit.x, unit.y, closest.x, closest.y) : Infinity;
      return distToCurrent < distToClosest ? current : closest;
    }, null);
  }

  // Original behavior: find the nearest enemy
  return livingEnemies.reduce((closest, current) => {
      const distToCurrent = getDistance(unit.x, unit.y, current.x, current.y);
      const distToClosest = closest ? getDistance(unit.x, unit.y, closest.x, closest.y) : Infinity;
      return distToCurrent < distToClosest ? current : closest;
    }, null);
}

function calculateCoverPosition(unit, tank, target) {
  const tankToEnemy = { x: target.x - tank.x, y: target.y - tank.y };
  const distance = Math.sqrt(tankToEnemy.x * tankToEnemy.x + tankToEnemy.y * tankToEnemy.y);
  const normalizedVector = { x: tankToEnemy.x / distance, y: tankToEnemy.y / distance };
  return { x: tank.x - normalizedVector.x * 50, y: tank.y - normalizedVector.y * 50 };
}

function resolveCollision(unit) {
  const allUnits = [...player1Units, ...player2Units].filter(u => u && u.health > 0);
  const unitSize = 40;
  for (let other of allUnits) {
    if (other.id !== unit.id) {
      let dist = getDistance(unit.x, unit.y, other.x, other.y);
      if (dist < unitSize) {
        let overlap = unitSize - dist;
        let angle = Math.atan2(unit.y - other.y, unit.x - other.x);
        unit.x += Math.cos(angle) * overlap * 0.5;
        unit.y += Math.sin(angle) * overlap * 0.5;
      }
    }
  }
}

function updateUnitPosition(unit) {
  const element = document.getElementById(unit.id);
  if (!element) return;

  // Stunned units cannot move
  if (unit.stunned || unit.frozen || unit.isRecharging || unit.sleeping) {
    return;
  }

  const dx = unit.targetX - unit.x;
  const dy = unit.targetY - unit.y;
  const distance = Math.sqrt(dx * dx + dy * dy);

  if (distance > 1) {
    const speed = unit.speed * 0.5;
    const moveX = (dx / distance) * speed;
    const moveY = (dy / distance) * speed;

    unit.x += moveX;
    unit.y += moveY;

    const battlefield = document.getElementById('battlefield');
    const battlefieldWidth = battlefield.clientWidth;
    const battlefieldHeight = battlefield.clientHeight;
    const unitSize = 40;
    unit.x = Math.min(Math.max(unit.x, 0), battlefieldWidth - unitSize);
    unit.y = Math.min(Math.max(unit.y, 0), battlefieldHeight - unitSize);

    if (unit.x <= 0) unit.targetX = 10;
    if (unit.x >= battlefieldWidth - unitSize) unit.targetX = battlefieldWidth - unitSize - 10;
    if (unit.y <= 0) unit.targetY = 10;
    if (unit.y >= battlefieldHeight - unitSize) unit.targetY = battlefieldHeight - unitSize - 10;

    resolveCollision(unit);
    element.style.left = `${unit.x}px`;
    element.style.top = `${unit.y}px`;
  }
}

function shootProjectile(attacker, target, effectType, damage, isCrit) {
  const battlefield = document.getElementById('battlefield');
  const projectile = document.createElement('div');
  projectile.className = `projectile ${effectType}`;

  if (effectType === 'arrow') {
    projectile.textContent = '➵';
    const startX = attacker.x + 20;
    const startY = attacker.y + 20;
    const endX = target.x + 20;
    const endY = target.y + 20;
    const dx = endX - startX;
    const dy = endY - startY;
    const angle = Math.atan2(dy, dx);
    projectile.style.transform = `rotate(${angle}rad)`;
  } else if (effectType === 'magic') {
    projectile.textContent = '💥';
  } else if (effectType === 'slash') {
    projectile.textContent = '⚔️';
  } else if (effectType === 'vampire') {
    projectile.textContent = '🦇';
  } else if (effectType === 'gun') {
    projectile.textContent = '•';
  }

  const startX = attacker.x + 20;
  const startY = attacker.y + 20;
  projectile.style.left = `${startX}px`;
  projectile.style.top = `${startY}px`;
  battlefield.appendChild(projectile);

  const endX = target.x + 20;
  const endY = target.y + 20;
  const dx = endX - startX;
  const dy = endY - startY;
  const angle = Math.atan2(dy, dx);

  setTimeout(() => {
    if (effectType === 'arrow') {
      projectile.style.transform = `translate(${dx}px, ${dy}px) rotate(${angle}rad)`;
    } else {
      projectile.style.transform = `translate(${dx}px, ${dy}px)`;
    }
    projectile.style.opacity = 0;
  }, 10);

  setTimeout(() => {
    projectile.remove();
    let damageType = 'normal';
    if (effectType === 'gun') {
      damageType = isCrit ? 'headshot' : 'normal';
    } else {
      damageType = isCrit ? 'crit' : 'normal';
    }
    showDamageText(target.x, target.y, damage, damageType);
  }, 410);
}

function attack(attacker, target) {
const now = Date.now();
if (now - attacker.lastAttack < attacker.attackCooldown) return;

// Check if attacker is stunned
if (attacker.stunned) {
return; // Stunned units cannot attack
}

// Check if attacker is frozen
if (attacker.frozen) {
return; // Frozen units cannot attack
}

// Check if target is recharging (cyborg) or immune
if (target.isRecharging || target.isImmune) {
return; // Cannot attack recharging cyborgs or immune units
}

const attackRange = getAttackRange(attacker);
const distance = getDistance(attacker.x, attacker.y, target.x, target.y);

if (distance <= attackRange) {
const hitRoll = Math.random() * 100;
if (hitRoll > attacker.accuracy) {
  showDamageText(target.x, target.y, 0, 'miss');
  attacker.lastAttack = now;
  return;
}

if (attacker.type === 'murder' && attacker.skillActive && Math.random() < 0.4) {
  // Cyborgs are immune to bleeding
  if (target.type === 'cyborg') {
    showDamageText(target.x, target.y, 'Immune to Bleeding', 'miss');
  } else {
    const bleedNow = Date.now();
    target.bleeding = {
      active: true,
      duration: 3000,
      damagePerSecond: 0.1 * target.maxHealth,
      lastTick: bleedNow
    };
    const targetEl = document.getElementById(target.id);
    if (targetEl) {
      const healthBar = targetEl.querySelector('.health-bar-fill');
      healthBar.classList.add('health-bar-bleeding');
    }
  }
}

if (attacker.attackEffect === 'gun') {
  const headshotRoll = Math.random() * 100;
  const isHeadshot = headshotRoll <= attacker.headshotChance;
  // If headshot, ignore defense
  let effectiveDefense = target.defense;
  if (target.sleeping) {
    effectiveDefense = target.defense * 0.2;
  }
  let damage = isHeadshot ? attacker.attack : Math.max(1, attacker.attack - effectiveDefense * 0.4);
  if (isHeadshot) damage *= 3.5;
  shootProjectile(attacker, target, 'gun', damage, isHeadshot);
  setTimeout(() => applyDamage(attacker, target, damage, isHeadshot), 410);
  attacker.lastAttack = now;
  return;
}

const critRoll = Math.random() * 100;
const isCrit = critRoll <= attacker.critChance;
let effectiveDefense = target.defense;
if (target.sleeping) {
  effectiveDefense = target.defense * 0.2;
}
let damage = Math.max(1, attacker.attack - effectiveDefense * 0.4);

if (attacker.type === 'archer' && attacker.skillActive) {
  damage = Math.floor(damage * 1.2);
}

if (isCrit) damage *= 1.75;

if (attacker.attackEffect === 'arrow') {
  shootProjectile(attacker, target, attacker.attackEffect, damage, isCrit);
  setTimeout(() => applyDamage(attacker, target, damage, isCrit), 410);
  
  // Range path double shot
  if (attacker.type === 'archer' && attacker.doubleShot && attacker.skillActive) {
    // Second arrow with slightly reduced damage
    const secondDamage = Math.floor(damage * 0.5);
    setTimeout(() => {
      shootProjectile(attacker, target, attacker.attackEffect, secondDamage, false);
      setTimeout(() => applyDamage(attacker, target, secondDamage, false), 410);
    }, 200); // 200ms delay between shots
  }
} else if (attacker.attackEffect === 'magic') {
  shootProjectile(attacker, target, attacker.attackEffect, damage, isCrit);
  setTimeout(() => {
    applyDamage(attacker, target, damage, isCrit);
    attacker.attackCount = (attacker.attackCount || 0) + 1;
  }, 410);
} else if (attacker.attackEffect === 'vampire') {
  // Enhanced vampire attack effect
  shootProjectile(attacker, target, 'vampire', damage, isCrit);

  // Create a visual drain effect (only if target is not cyborg)
  if (target.type !== 'cyborg') {
    const battlefield = document.getElementById('battlefield');
    const drainEffect = document.createElement('div');
    drainEffect.className = 'drain-effect';
    drainEffect.style.position = 'absolute';
    drainEffect.style.left = `${target.x}px`;
    drainEffect.style.top = `${target.y}px`;
    drainEffect.style.width = '40px';
    drainEffect.style.height = '40px';
    drainEffect.style.background = 'radial-gradient(circle, rgba(255,0,0,0.5) 0%, rgba(0,0,0,0) 70%)';
    drainEffect.style.borderRadius = '50%';
    drainEffect.style.zIndex = '100';
    drainEffect.style.pointerEvents = 'none';
    battlefield.appendChild(drainEffect);

    // Animate the drain effect
    const startX = target.x;
    const startY = target.y;
    const endX = attacker.x;
    const endY = attacker.y;

    // Create animation
    const animateDrain = () => {
      const progress = (Date.now() - startTime) / 400; // 400ms animation
      if (progress >= 1) {
        drainEffect.remove();
        return;
      }

      const currentX = startX + (endX - startX) * progress;
      const currentY = startY + (endY - startY) * progress;

      drainEffect.style.left = `${currentX}px`;
      drainEffect.style.top = `${currentY}px`;
      drainEffect.style.opacity = (1 - progress).toString();

      requestAnimationFrame(animateDrain);
    };

    const startTime = Date.now();
    requestAnimationFrame(animateDrain);
  }

  setTimeout(() => {
    applyDamage(attacker, target, damage, isCrit);

    // Vampire's special ability counter (lifesteal is now handled in applyDamage)
    attacker.lifestealCount = (attacker.lifestealCount || 0) + 1;
    if (attacker.lifestealCount >= 5) {
      attacker.speed *= 1.05;
      attacker.attack += 0.5
      attacker.attackSpeed -= 0.5;
      attacker.lifestealCount = 0;
      const attackerEl = document.getElementById(attacker.id);
      if (attackerEl) {
        attackerEl.style.boxShadow = '0 0 50px red';
        setTimeout(() => attackerEl.style.boxShadow = '', 1000);
      }
    }
  }, 410);
} else {
  shootProjectile(attacker, target, 'slash', damage, isCrit);
  setTimeout(() => {
    applyDamage(attacker, target, damage, isCrit);
  }, 410);
}

// Berserk attack stack system
if (attacker.type === 'berserk') {
  attacker.attackStack = (attacker.attackStack || 0) + 1;

  if (attacker.attackStack >= 3) {
    attacker.nextAttackDrain = true;
    attacker.attackStack = 0;

    // Visual effect for next attack drain
    const attackerEl = document.getElementById(attacker.id);
    if (attackerEl) {
      attackerEl.style.border = '3px solid #ff0000';
      attackerEl.style.boxShadow = '0 0 20px #ff0000';
      setTimeout(() => {
        if (attackerEl) {
          attackerEl.style.border = '';
          attackerEl.style.boxShadow = '';
        }
      }, 2000);
    }
  }
}

attacker.lastAttack = now;
}

// --- Cyborg Power Punch Mechanic ---
if (attacker.type === 'cyborg') {
  attacker.cyborgAttackCount = (attacker.cyborgAttackCount || 0) + 1;
  const requiredAttacks = attacker.path === 'path1' ? 3 : 5; // Path1: 3 attacks, Path2: 5 attacks
  if (attacker.cyborgAttackCount >= requiredAttacks) {
    // AOE bomb attack: big damage to all enemies in range
    const aoeRadius = 120; // Large explosion radius
    const baseDamage = Math.floor(attacker.attack * 3);
    
    // Get all enemy units
    const enemies = attacker.player === 1 ? player2Units : player1Units;
    const hitEnemies = [];
    
    enemies.forEach(enemy => {
      if (enemy && enemy.health > 0 && !enemy.isRecharging) {
        const dist = getDistance(target.x, target.y, enemy.x, enemy.y);
        if (dist <= aoeRadius) {
          hitEnemies.push(enemy);
        }
      }
    });
    
    // Shoot projectile to main target
    shootProjectile(attacker, target, 'cyborgpunch', baseDamage, true);
    
    setTimeout(() => {
      // Apply damage to all enemies in range
      hitEnemies.forEach(enemy => {
        const damage = Math.max(1, baseDamage - enemy.defense * 0.4);
        applyDamage(attacker, enemy, damage, true);
        showDamageText(enemy.x, enemy.y, damage, 'crit');
      });
      
      // Add explosion effect
      const battlefield = document.getElementById('battlefield');
      const explosion = document.createElement('div');
      explosion.className = 'explosion-effect';
      explosion.style.left = `${target.x - 40}px`;
      explosion.style.top = `${target.y - 40}px`;
      explosion.style.width = '160px';
      explosion.style.height = '160px';
      battlefield.appendChild(explosion);
      setTimeout(() => explosion.remove(), 800);
    }, 410);
    
    attacker.lastAttack = now;
    attacker.cyborgAttackCount = 0;
    return;
  }
}
}

// Function to check passive abilities (no cooldown restrictions)
function checkPassiveAbilities(unit) {
  const now = Date.now();
  const unitEl = document.getElementById(unit.id);

  switch (unit.type) {
    case 'ninja':
      // Reset hasVanished when health is back above 50%
      if (unit.health >= unit.maxHealth * 0.5 && unit.hasVanished) {
        unit.hasVanished = false;
      }
      break;

    case 'berserk':
      // Berserk passive: activate when health drops below 50%, deactivate when above 50%
      if (unit.health < unit.maxHealth * 0.5 && !unit.skillActive) {
        unit.skillActive = true;
        unit.lastSkillTime = now;

        if (unit.path === 'path1') {
          unit.defense = unit.originalDefense * 5; // Greatly increase defense
          unit.lifesteal = (unit.lifesteal || 0) + 0.15; // Add 15% lifesteal
          if (unitEl) unitEl.style.boxShadow = '0 0 50px cyan';
        } else if (unit.path === 'path2') {
          unit.isImmune = true; // Become immune to damage
          unit.lifesteal = (unit.lifesteal || 0) + 0.20; // Add 20% lifesteal
          if (unitEl) unitEl.style.boxShadow = '0 0 50px white';
          setTimeout(() => {
            if (unit && unit.isImmune) {
              unit.isImmune = false;
              if (unitEl) unitEl.style.boxShadow = '';
            }
          }, 2000); // 2-second duration
        } else {
          // Original skill for levels < 3
          unit.attack = unit.originalAttack * 1.7;
          unit.attackSpeed = unit.originalAttackSpeed * 1.5; // Fixed: was subtracting 3
          unit.defense = unit.originalDefense * 0.3;
          unit.maxHealth = unit.maxHealth - 20;
          unit.critChance = unit.critChance * 1.3;
          unit.speed = unit.originalSpeed * 1.7;
          unit.lifesteal = (unit.lifesteal || 0) + 0.10; // Add 10% lifesteal for basic berserk
          unit.attackCooldown = 1000 / unit.attackSpeed; // Update attack cooldown
          if (unitEl) unitEl.style.boxShadow = '0 0 50px red';
        }
      } else if (unit.health >= unit.maxHealth * 0.5 && unit.skillActive) {
        // Deactivate berserk when health goes back above 50%
        unit.skillActive = false;

        if (unit.path === 'path1') {
          unit.defense = unit.originalDefense;
          unit.lifesteal = Math.max(0, (unit.lifesteal || 0) - 0.15); // Remove lifesteal
          if (unitEl) unitEl.style.boxShadow = '';
        } else if (unit.path === 'path2') {
          // Path2 immunity is handled by timeout, but reset visual effects
          unit.lifesteal = Math.max(0, (unit.lifesteal || 0) - 0.20); // Remove lifesteal
          if (unitEl) unitEl.style.boxShadow = '';
        } else {
          // Reset original skill effects
          unit.attack = unit.originalAttack;
          unit.attackSpeed = unit.originalAttackSpeed;
          unit.defense = unit.originalDefense;
          unit.maxHealth = unit.maxHealth + 20; // Restore the reduced health
          unit.critChance = unit.critChance / 1.3;
          unit.speed = unit.originalSpeed;
          unit.lifesteal = Math.max(0, (unit.lifesteal || 0) - 0.10); // Remove lifesteal
          unit.attackCooldown = 1000 / unit.attackSpeed;
          if (unitEl) unitEl.style.boxShadow = '';
        }
      }
      break;
  }
}

function tryActivateSkill(unit, allies, enemies) {
  // ถ้าเป็น story mode และ unit.player === 2 ให้ลบ path ออก (บังคับใช้สกิล default เท่านั้น)
  if (gameState.currentMode === 'story' && unit.player === 2 && unit.path) {
    delete unit.path;
  }
  const now = Date.now();

  // Apply cooldown reduction from items
  let effectiveCooldown = unit.specialCooldown;
  if (unit.skillCooldownReduction) {
    effectiveCooldown = unit.specialCooldown * (1 - unit.skillCooldownReduction);
  }

  if (now - unit.lastSkillTime < effectiveCooldown || unit.skillActive) return;

  const unitEl = document.getElementById(unit.id);

  // Set default multipliers for skills
  const safeAttackMultiplier = 1.0;
  const safeDefenseMultiplier = 1.0;
  const safeSpeedMultiplier = 1.0;

  switch (unit.type) {
    // Berserk is now handled in checkPassiveAbilities function
    // case 'berserk': - REMOVED FROM HERE

    case 'archer':
      const archerTarget = findTarget(unit, enemies);
      if (archerTarget) {
        unit.skillActive = true;
        unit.lastSkillTime = now;
        
        if (unit.path === 'path1') {
          // Path1: shoot two arrows at once
          unit.attackSpeed = unit.originalAttackSpeed * 2.5;
          unit.attackCooldown = 1000 / unit.attackSpeed;
          unit.doubleShot = true; // Flag for double shot
          if (unitEl) unitEl.style.filter = 'brightness(1.5)';
          setTimeout(() => {
            if (unit && unit.skillActive) {
              unit.skillActive = false;
              unit.attackSpeed = unit.originalAttackSpeed;
              unit.attackCooldown = 1000 / unit.attackSpeed;
              unit.doubleShot = false;
              if (unitEl) unitEl.style.filter = '';
            }
          }, unit.skillDuration);
        } else if (unit.path === 'path2') {
          // Path2: Enhanced rapid fire (active skill)
          unit.attackSpeed = unit.originalAttackSpeed * 2.2;
          unit.attackCooldown = 1000 / unit.attackSpeed;
          unit.attack = unit.originalAttack * 1.5;
          if (unitEl) unitEl.style.filter = 'brightness(1.5)';
          setTimeout(() => {
            if (unit && unit.skillActive) {
              unit.skillActive = false;
              unit.attackSpeed = unit.originalAttackSpeed;
              unit.attackCooldown = 1000 / unit.attackSpeed;
              unit.attack = unit.originalAttack;
              if (unitEl) unitEl.style.filter = '';
            }
          }, unit.skillDuration);
        } else {
          // Original skill for levels < 3
          unit.attackSpeed = unit.originalAttackSpeed * 2.5;
          unit.attackCooldown = 1000 / unit.attackSpeed;
          if (unitEl) unitEl.style.filter = 'brightness(1.5)';
          setTimeout(() => {
            if (unit && unit.skillActive) {
              unit.skillActive = false;
              unit.attackSpeed = unit.originalAttackSpeed;
              unit.attackCooldown = 1000 / unit.attackSpeed;
              if (unitEl) unitEl.style.filter = '';
            }
          }, unit.skillDuration);
        }
      }
      break;

    case 'tank':
      // Tank skills are passive and triggered in applyDamage
      break;

    case 'mage':
      if (unit.path === 'path1') {
        // Path1: Enhanced meteor with reduced cooldown
        if ((unit.attackCount || 0) >= 7) { // Reduced from 10 to 7
          unit.lastSkillTime = now;
          castEnhancedMeteor(unit, enemies);
          unit.attackCount = 0;
        }
      } else if (unit.path === 'path2') {
        // Path2: Blue ice meteor that freezes enemies
        if ((unit.attackCount || 0) >= 6) { // Faster cooldown for path2
          unit.lastSkillTime = now;
          castIceMeteor(unit, enemies);
          unit.attackCount = 0;
        }
      } else {
        // Original mage skill
        if ((unit.attackCount || 0) >= 10) {
          unit.lastSkillTime = now;
          castMeteor(unit, enemies);
          unit.attackCount = 0;
        }
      }
      break;

    case 'knight':
      unit.skillActive = true;
      unit.lastSkillTime = now;
      // Apply skill effects
      unit.attack = unit.originalAttack * 1.5;
      unit.defense = unit.originalDefense * 1.8;
      unit.speed = unit.originalSpeed * 1.5;
      unit.attackSpeed = unit.originalAttackSpeed * 1.5;
      unit.attackCooldown = 1000 / unit.attackSpeed;
      if (unitEl) unitEl.style.border = '2px solid blue';
      setTimeout(() => {
        if (unit && unit.skillActive) {
          unit.skillActive = false;
          unit.attack = unit.originalAttack;
          unit.defense = unit.originalDefense;
          unit.speed = unit.originalSpeed;
          unit.attackSpeed = unit.originalAttackSpeed;
          unit.attackCooldown = 1000 / unit.attackSpeed;
          if (unitEl) unitEl.style.border = '';
        }
      }, unit.skillDuration);
      break;

    case 'ninja':
      // Ninja warp skill - only activate once when health drops below 50%
      if (unit.health < unit.maxHealth * 0.5 && !unit.hasVanished && !unit.skillActive) {
        unit.hasVanished = true;
        unit.skillActive = true;
        unit.lastSkillTime = now;
        unit.windWalkActive = true;
        const enemiesList = unit.player === 1 ? player2Units.filter(u => u && u.health > 0) : player1Units.filter(u => u && u.health > 0);
        if (enemiesList.length > 0) {
          const backEnemy = findBackEnemy(enemiesList, unit.player);
          // Enhance teleport distance based on speed multiplier
          const offset = 40 * safeSpeedMultiplier;
          let newX = unit.player === 1 ? backEnemy.x + offset : backEnemy.x - offset;
          let newY = backEnemy.y;
          const battlefield = document.getElementById('battlefield');
          unit.x = newX;
          unit.y = newY;
          if (unitEl) {
            unitEl.style.left = `${unit.x}px`;
            unitEl.style.top = `${unit.y}px`;
          }
        }
        if (unitEl) {
          unitEl.style.opacity = '0.5';
          unitEl.style.boxShadow = '0 0 50px blue';
        }
        setTimeout(() => {
          if (unit && unit.skillActive) {
            unit.skillActive = false;
            unit.windWalkActive = false;
            // Don't reset hasVanished here - it should stay true to prevent re-activation
            if (unitEl) {
              unitEl.style.opacity = '1';
              unitEl.style.boxShadow = '';
            }
          }
        }, unit.skillDuration);
      }
      
      // Clone skill for Path 1
      if (unit.path === 'path1' && now - (unit.lastCloneSkillTime || 0) >= 12000) { // Cooldown reduced to 12s
        unit.lastCloneSkillTime = now;
        createClones(unit, 2);
        if (unitEl) {
            unitEl.style.boxShadow = '0 0 50px green';
            setTimeout(() => { if (unitEl) unitEl.style.boxShadow = ''; }, 1000);
        }
      }
      break;

    case 'murder':
      unit.skillActive = true;
      unit.lastSkillTime = now;
      // Apply skill effects
      unit.attack = unit.originalAttack * 2.5;
      unit.attackSpeed = unit.originalAttackSpeed * 1.6;
      unit.speed = unit.originalSpeed * 1.5;
      unit.defense = unit.originalDefense * 0.7;
      unit.critChance = unit.critChance * 1.3;
      unit.attackCooldown = 1000 / unit.attackSpeed;
      if (unitEl) unitEl.style.boxShadow = '0 0 50px red';
      setTimeout(() => {
        if (unit && unit.skillActive) {
          unit.skillActive = false;
          unit.attack = unit.originalAttack;
          unit.attackSpeed = unit.originalAttackSpeed;
          unit.speed = unit.originalSpeed;
          unit.defense = unit.originalDefense;
          unit.critChance = unit.critChance / 1.3;
          unit.attackCooldown = 1000 / unit.attackSpeed;
          if (unitEl) unitEl.style.boxShadow = '';
        }
      }, unit.skillDuration);
      break;

    case 'vampire':
      if (unit.path === 'path1') {
        // Can only use if above 60% HP and not already active
        if (!unit.skillActive && unit.health > unit.maxHealth * 0.6) {
          unit.skillActive = true;
          unit.lastSkillTime = now;
          const target = findTarget(unit, enemies);
          if (target) {
            const sacrifice = Math.floor(unit.maxHealth * 0.1);
            unit.health -= sacrifice;
            if (unitEl) {
              const healthBar = unitEl.querySelector('.health-bar-fill');
              healthBar.style.width = `${(unit.health / unit.maxHealth) * 100}%`;
              unitEl.style.boxShadow = '0 0 50px #a00';
            }
            showDamageText(unit.x, unit.y, sacrifice, 'miss');
            // --- เพิ่ม Immortal ขั้นสาม ---
            if (unit.hasImmortal) {
              handleVampireImmortal(unit, 1000);
            }
            // --- Blood Spear Projectile Effect ---
            const battlefield = document.getElementById('battlefield');
            const bloodSpear = document.createElement('div');
            bloodSpear.className = 'blood-spear-projectile';
            // Make spear horizontal and center-aligned like archer's arrow
            const spearW = 60, spearH = 12;
            bloodSpear.style.width = spearW + 'px';
            bloodSpear.style.height = spearH + 'px';
            // Center of vampire
            const startX = unit.x + 20 - spearW / 2;
            const startY = unit.y + 20 - spearH / 2;
            // Center of target
            const endX = target.x + 20 - spearW / 2;
            const endY = target.y + 20 - spearH / 2;
            bloodSpear.style.left = `${startX}px`;
            bloodSpear.style.top = `${startY}px`;
            const dx = endX - startX;
            const dy = endY - startY;
            const angle = Math.atan2(dy, dx);
            bloodSpear.style.transform = `rotate(${angle}rad)`;
            battlefield.appendChild(bloodSpear);
            setTimeout(() => {
              bloodSpear.style.transform = `translate(${dx}px, ${dy}px) rotate(${angle}rad)`;
              bloodSpear.style.opacity = 1;
            }, 10);
            setTimeout(() => {
              bloodSpear.remove();
              // --- Blood Explosion Effect ---
              const explosion = document.createElement('div');
              explosion.className = 'blood-explosion-effect';
              explosion.style.left = `${endX - 40}px`;
              explosion.style.top = `${endY - 40}px`;
              battlefield.appendChild(explosion);
              setTimeout(() => explosion.remove(), 700);
              // --- Blood Splatter ---
              for (let i = 0; i < 8; i++) {
                const splatter = document.createElement('div');
                splatter.className = 'blood-splatter';
                splatter.style.left = `${endX}px`;
                splatter.style.top = `${endY}px`;
                const angle = Math.random() * 2 * Math.PI;
                const dist = 30 + Math.random() * 30;
                const dx = Math.cos(angle) * dist;
                const dy = Math.sin(angle) * dist;
                splatter.style.transform = `translate(0,0)`;
                battlefield.appendChild(splatter);
                setTimeout(() => {
                  splatter.style.transform = `translate(${dx}px, ${dy}px) scale(${0.7 + Math.random() * 0.6})`;
                  splatter.style.opacity = 0.7;
                }, 30);
                setTimeout(() => splatter.remove(), 700 + Math.random() * 300);
              }
              // --- Apply damage after effect ---
              if (target.health > 0) {
                target.health = Math.max(0, target.health - sacrifice);
                const targetEl = document.getElementById(target.id);
                if (targetEl) {
                  const healthBar = targetEl.querySelector('.health-bar-fill');
                  healthBar.style.width = `${(target.health / target.maxHealth) * 100}%`;
                  createHitEffect(target.id);
                }
                showDamageText(target.x, target.y, sacrifice, 'crit');
                if (target.health <= 0) {
                  handleUnitDeath(unit, target);
                }
              }
              if (unitEl) setTimeout(() => { unitEl.style.boxShadow = ''; }, 600);
              unit.skillActive = false;
              // --- เพิ่ม wind walk 3 วิ หลังปล่อย spear ---
              unit.windWalkActive = true;
              unit.isImmune = true;
              if (unitEl) {
                unitEl.style.opacity = '0.5';
                unitEl.style.boxShadow = '0 0 50px #00eaff';
              }
              setTimeout(() => {
                unit.windWalkActive = false;
                unit.isImmune = false;
                if (unitEl) {
                  unitEl.style.opacity = '1';
                  unitEl.style.boxShadow = '';
                }
              }, 3000); // 3 วินาที
              if (unitEl) setTimeout(() => { unitEl.style.boxShadow = ''; }, 600);
              unit.skillActive = false;
            }, 400);
          } else {
            unit.skillActive = false;
          }
        }
      } else if (unit.path === 'path2') {
        // Path2: Blood Rage - temporary boost
        if (!unit.skillActive) {
          unit.skillActive = true;
          unit.lastSkillTime = now;
          unit.attack = unit.originalAttack * 2.0;
          unit.attackSpeed = unit.originalAttackSpeed * 1.5;
          unit.attackCooldown = 1000 / unit.attackSpeed;
          unit.lifesteal = (unit.lifesteal || 0.15) + 0.3; // Temporary lifesteal boost
          if (unitEl) unitEl.style.boxShadow = '0 0 50px #ff0000';
          setTimeout(() => {
            if (unit && unit.skillActive) {
              unit.skillActive = false;
              unit.attack = unit.originalAttack;
              unit.attackSpeed = unit.originalAttackSpeed;
              unit.attackCooldown = 1000 / unit.attackSpeed;
              unit.lifesteal = (unit.lifesteal || 0.15) - 0.3; // Reset lifesteal
              if (unitEl) unitEl.style.boxShadow = '';
            }
          }, unit.skillDuration);
        }
      }
      break;

    case 'gunman':
      if (unit.path === 'path1') {
        // Path1: Multi-shot mode
        if (!unit.skillActive) {
          unit.skillActive = true;
          unit.lastSkillTime = now;
          unit.attackSpeed = unit.originalAttackSpeed * 2.0;
          unit.attackCooldown = 1000 / unit.attackSpeed;
          unit.multiShotMode = true;
          if (unitEl) unitEl.style.filter = 'brightness(1.5) saturate(1.5)';
          setTimeout(() => {
            if (unit && unit.skillActive) {
              unit.skillActive = false;
              unit.attackSpeed = unit.originalAttackSpeed;
              unit.attackCooldown = 1000 / unit.attackSpeed;
              unit.multiShotMode = false;
              if (unitEl) unitEl.style.filter = '';
            }
          }, unit.skillDuration);
        }
      } else if (unit.path === 'path2') {
        // Path2: Sniper mode
        if (!unit.skillActive) {
          unit.skillActive = true;
          unit.lastSkillTime = now;
          unit.attack = unit.originalAttack * 1.08;
          if (unitEl) unitEl.style.filter = 'brightness(1.3) contrast(1.5)';
          setTimeout(() => {
            if (unit && unit.skillActive) {
              unit.skillActive = false;
              unit.attack = unit.originalAttack;
              if (unitEl) unitEl.style.filter = '';
            }
          }, unit.skillDuration);
        }
      }
      break;

    case 'reaper':
      if (!unit.skillActive) {
        unit.skillActive = true;
        unit.lastSkillTime = now;
        unit.attack = unit.originalAttack * 1.8;
        if (unitEl) unitEl.style.boxShadow = '0 0 50px #800080';
        setTimeout(() => {
          if (unit && unit.skillActive) {
            unit.skillActive = false;
            unit.attack = unit.originalAttack;
            if (unitEl) unitEl.style.boxShadow = '';
          }
        }, unit.skillDuration);
      }
      break;

    case 'ghost':
      if (!unit.skillActive && unit.form === 'human') {
        unit.skillActive = true;
        unit.lastSkillTime = now;
        // Transform to ghost form temporarily
        unit.attack = unit.originalAttack * 1.5;
        unit.speed = unit.originalSpeed * 2.0;
        unit.attackSpeed = unit.originalAttackSpeed * 1.5;
        unit.attackCooldown = 1000 / unit.attackSpeed;
        if (unitEl) {
          unitEl.style.opacity = '0.7';
          unitEl.style.filter = 'brightness(1.3) hue-rotate(180deg)';
        }
        setTimeout(() => {
          if (unit && unit.skillActive) {
            unit.skillActive = false;
            unit.attack = unit.originalAttack;
            unit.speed = unit.originalSpeed;
            unit.attackSpeed = unit.originalAttackSpeed;
            unit.attackCooldown = 1000 / unit.attackSpeed;
            if (unitEl) {
              unitEl.style.opacity = '1';
              unitEl.style.filter = '';
            }
          }
        }, unit.skillDuration);
      }
      break;

    case 'cyborg':
      // Original cyborg shield skill (no paths)
      if (!unit.skillActive && !unit.isRecharging) {
        unit.skillActive = true;
        unit.lastSkillTime = now;
        unit.defense = unit.originalDefense * 3; // Massive defense boost
        if (unitEl) {
          unitEl.style.boxShadow = '0 0 50px #00eaff, 0 0 100px #00eaff';
        }
        setTimeout(() => {
          if (unit) {
            unit.skillActive = false;
            unit.defense = unit.originalDefense;
            if (unitEl) unitEl.style.boxShadow = '';
          }
        }, 4000); // Shield lasts 4 seconds
      }
      break;

    case 'tungtung': {
      // --- Sleep skill (teleport and sleep enemy) ---
      if (unit.skillActive) break;
      if (now - unit.lastSkillTime < unit.specialCooldown) break;
      // Find enemies
      const enemyUnits = unit.player === 1 ? enemies.filter(u => u && u.health > 0 && !u.sleeping) : enemies.filter(u => u && u.health > 0 && !u.sleeping);
      if (enemyUnits.length === 0) break;
      // Pick a random enemy
      const target = enemyUnits[Math.floor(Math.random() * enemyUnits.length)];
      if (!target) break;
      unit.skillActive = true;
      unit.lastSkillTime = now;
      // Store original range
      target._originalRange = target.range;
      target.sleeping = true;
      target.range = 0;
      // Visual sleep effect
      const targetEl = document.getElementById(target.id);
      if (targetEl) {
        targetEl.style.filter = 'grayscale(100%) blur(2px)';
        const sleepIcon = document.createElement('div');
        sleepIcon.className = 'sleep-effect';
        sleepIcon.textContent = '💤';
        sleepIcon.style.position = 'absolute';
        sleepIcon.style.top = '-20px';
        sleepIcon.style.left = '50%';
        sleepIcon.style.transform = 'translateX(-50%)';
        sleepIcon.style.fontSize = '20px';
        sleepIcon.style.zIndex = '10';
        sleepIcon.id = `${target.id}-sleep`;
        targetEl.appendChild(sleepIcon);
        // Show message above target
        const msg = document.createElement('div');
        msg.className = 'damage-text';
        msg.style.color = '#00bfff';
        msg.style.fontWeight = 'bold';
        msg.style.top = '-35px';
        msg.style.left = '0';
        msg.textContent = 'Sleep!';
        targetEl.appendChild(msg);
        setTimeout(() => msg.remove(), 1200);
      }
      // Add glow to tungtung
      const unitEl = document.getElementById(unit.id);
      if (unitEl) {
        unitEl.style.boxShadow = '0 0 30px 10px #00bfff, 0 0 60px 20px #fff';
      }


      // After 3s, wake up original target
      setTimeout(() => {
        target.sleeping = false;
        if (typeof target._originalRange !== 'undefined') {
          target.range = target._originalRange;
          delete target._originalRange;
        }
        if (targetEl) {
          targetEl.style.filter = '';
          const sleepIcon = document.getElementById(`${target.id}-sleep`);
          if (sleepIcon) sleepIcon.remove();
        }
      }, 3000);
      // Teleport behind target
      const originalX = unit.x;
      const originalY = unit.y;
      const offset = 40;
      const behindX = target.x + (unit.player === 1 ? -offset : offset);
      const behindY = target.y;
      if (unitEl) {
        setTimeout(() => {
          unitEl.style.opacity = '0';
          setTimeout(() => {
            unit.x = behindX;
            unit.y = behindY;
            unitEl.style.left = `${unit.x}px`;
            unitEl.style.top = `${unit.y}px`;
            unitEl.style.opacity = '1';
            // Attack with high damage
            const damage = unit.attack * 6;
            shootProjectile(unit, target, 'slash', damage, true);
            setTimeout(() => {
              applyDamage(unit, target, damage, true);
            }, 410);
            // Teleport back after attack
            setTimeout(() => {
              unitEl.style.opacity = '0';
              setTimeout(() => {
                unit.x = originalX;
                unit.y = originalY;
                unitEl.style.left = `${unit.x}px`;
                unitEl.style.top = `${unit.y}px`;
                unitEl.style.opacity = '1';
                unit.skillActive = false;
                unitEl.style.boxShadow = '';

                // 50% chance to use sleep again on another target after warping back
                const bonusSleepRoll = Math.random() * 100;
                if (bonusSleepRoll <= 50) {
                  // Find another enemy that's not sleeping
                  const availableTargets = enemyUnits.filter(u => u && u.health > 0 && !u.sleeping && u.id !== target.id);
                  if (availableTargets.length > 0) {
                    const bonusTarget = availableTargets[Math.floor(Math.random() * availableTargets.length)];

                    // Warp to bonus target and attack + sleep
                    setTimeout(() => {
                      // Warp to bonus target
                      unitEl.style.opacity = '0';
                      setTimeout(() => {
                        const offset = unit.player === 1 ? -30 : 30;
                        unit.x = bonusTarget.x + offset;
                        unit.y = bonusTarget.y;
                        unitEl.style.left = `${unit.x}px`;
                        unitEl.style.top = `${unit.y}px`;
                        unitEl.style.opacity = '1';
                        unitEl.style.boxShadow = '0 0 50px #00bfff';

                        // Attack bonus target
                        setTimeout(() => {
                          applyDamage(unit, bonusTarget, unit.attack, false);
                          createHitEffect(bonusTarget.id);

                          // Apply sleep to bonus target
                          bonusTarget._originalRange = bonusTarget.range;
                          bonusTarget.sleeping = true;
                          bonusTarget.range = 0;

                          const bonusTargetEl = document.getElementById(bonusTarget.id);
                          if (bonusTargetEl) {
                            bonusTargetEl.style.filter = 'grayscale(100%) blur(2px)';
                            const bonusSleepIcon = document.createElement('div');
                            bonusSleepIcon.className = 'sleep-effect';
                            bonusSleepIcon.textContent = '💤';
                            bonusSleepIcon.style.position = 'absolute';
                            bonusSleepIcon.style.top = '-20px';
                            bonusSleepIcon.style.left = '50%';
                            bonusSleepIcon.style.transform = 'translateX(-50%)';
                            bonusSleepIcon.style.fontSize = '20px';
                            bonusSleepIcon.style.zIndex = '10';
                            bonusSleepIcon.id = `${bonusTarget.id}-sleep`;
                            bonusTargetEl.appendChild(bonusSleepIcon);

                            const bonusMsg = document.createElement('div');
                            bonusMsg.className = 'damage-text';
                            bonusMsg.style.color = '#ff69b4';
                            bonusMsg.style.fontWeight = 'bold';
                            bonusMsg.style.top = '-35px';
                            bonusMsg.style.left = '0';
                            bonusMsg.textContent = 'Bonus Sleep!';
                            bonusTargetEl.appendChild(bonusMsg);
                            setTimeout(() => bonusMsg.remove(), 1200);
                          }

                          // Wake up bonus target after 3s
                          setTimeout(() => {
                            bonusTarget.sleeping = false;
                            if (typeof bonusTarget._originalRange !== 'undefined') {
                              bonusTarget.range = bonusTarget._originalRange;
                              delete bonusTarget._originalRange;
                            }
                            if (bonusTargetEl) {
                              bonusTargetEl.style.filter = '';
                              const bonusSleepIcon = document.getElementById(`${bonusTarget.id}-sleep`);
                              if (bonusSleepIcon) bonusSleepIcon.remove();
                            }
                          }, 3000);

                          // Warp back to original position after bonus attack
                          setTimeout(() => {
                            unitEl.style.opacity = '0';
                            setTimeout(() => {
                              unit.x = originalX;
                              unit.y = originalY;
                              unitEl.style.left = `${unit.x}px`;
                              unitEl.style.top = `${unit.y}px`;
                              unitEl.style.opacity = '1';
                              unitEl.style.boxShadow = '';
                            }, 200);
                          }, 300);

                        }, 200);
                      }, 200);
                    }, 300);
                  }
                }
              }, 200);
            }, 500);
          }, 200);
        }, 200);
      } else {
        setTimeout(() => { unit.skillActive = false; }, 900);
      }
      // Play tung-tung-sahur.mp3 sound
      try {
        let audio = document.getElementById('tungtung-sleep-audio');
        if (!audio) {
          audio = document.createElement('audio');
          audio.id = 'tungtung-sleep-audio';
          audio.src = 'tung-tung-sahur.mp3';
          audio.preload = 'auto';
          document.body.appendChild(audio);
        }
        audio.currentTime = 0;
        audio.play();
      } catch (e) { /* ignore audio errors */ }
      break;
    }
  }
}

function castMeteor(mage, enemies) {
  const battlefield = document.getElementById('battlefield');
  const target = findTarget(mage, enemies);
  if (!target) return;
  const meteorX = target.x + 20;
  const meteorY = target.y + 20;
  const meteor = document.createElement('div');
  meteor.className = 'projectile meteor';
  meteor.textContent = '☄️';
  const startX = meteorX;
  const startY = -100;
  meteor.style.left = `${startX}px`;
  meteor.style.top = `${startY}px`;
  battlefield.appendChild(meteor);

  setTimeout(() => {
    meteor.style.transform = `translate(0, ${meteorY - startY}px)`;
  }, 10);

  setTimeout(() => {
    meteor.remove();
    const explosion = document.createElement('div');
    explosion.className = 'explosion-effect';
    explosion.style.left = `${target.x - 30}px`;
    explosion.style.top = `${target.y - 30}px`;
    battlefield.appendChild(explosion);
    setTimeout(() => explosion.remove(), 800);

    // Calculate meteor damage
    const aoeRadius = 100;
    const meteorDamage = Math.floor(mage.attack * 3);

    enemies.forEach(enemy => {
      if (!enemy) return; // Skip null/undefined enemies
      const dist = getDistance(target.x, target.y, enemy.x, enemy.y);
      if (dist <= aoeRadius && enemy.health > 0) {
        enemy.health = Math.max(0, enemy.health - meteorDamage);
        const enemyEl = document.getElementById(enemy.id);
        if (enemyEl) {
          const healthBar = enemyEl.querySelector('.health-bar-fill');
          healthBar.style.width = `${(enemy.health / enemy.maxHealth) * 100}%`;
        }
        showDamageText(enemy.x, enemy.y, meteorDamage, 'crit');
        if (enemy.health <= 0) {
          handleUnitDeath(mage, enemy);
        }
      }
    });

    const boomText = document.createElement('div');
    boomText.className = 'damage-text crit';
    boomText.textContent = 'BOOM!';
    boomText.style.left = `${target.x}px`;
    boomText.style.top = `${target.y}px`;
    battlefield.appendChild(boomText);
    setTimeout(() => boomText.remove(), 1000);
  }, 710);
}

function applyDamage(attacker, target, damage, isCrit) {
  if (target.isImmune) {
    showDamageText(target.x, target.y, 'Immune', 'miss');
    return;
  }

  if (target.type === 'ninja' && target.windWalkActive) {
    showDamageText(target.x, target.y, "Evaded!", 'miss');

    // Apply evasion effect
    const targetEl = document.getElementById(target.id);
    if (targetEl) {
      targetEl.style.filter = 'blur(5px)';
      setTimeout(() => targetEl.style.filter = '', 300);
    }

    return;
  }

  // --- TUNGTUNG SLEEP BUFF: Reduce defense to 20% if sleeping ---
  let effectiveDefense = target.defense;
  if (target.sleeping) {
    effectiveDefense = target.defense * 0.2;
  }

  // Tank shield skill: activate when taking damage if cooldown is ready
  if (target.type === 'tank' && !target.skillActive) {
    const now = Date.now();
    if (now - target.lastSkillTime >= target.specialCooldown) {
      target.skillActive = true;
      target.lastSkillTime = now;
      
      if (target.path === 'path2') {
        // Path2: protect all allies
        const allies = target.player === 1 ? player1Units : player2Units;
        allies.forEach(ally => {
          if (ally && ally.health > 0) {
            ally.defense = ally.originalDefense * 3;
            ally.superTeamProtected = true; // Mark as protected by Path2
            const allyEl = document.getElementById(ally.id);
            if (allyEl) allyEl.style.border = '2px solid cyan';
          }
        });
        setTimeout(() => {
          allies.forEach(ally => {
            if (ally && ally.health > 0) {
              ally.defense = ally.originalDefense;
              ally.superTeamProtected = false; // Remove protection
              const allyEl = document.getElementById(ally.id);
              if (allyEl) allyEl.style.border = '';
            }
          });
          target.skillActive = false;
        }, target.skillDuration);
      } else {
        // Original tank skill or Path1
        target.defense = target.originalDefense * 3;
        const targetEl = document.getElementById(target.id);
        if (targetEl) targetEl.style.border = '2px solid cyan';
        setTimeout(() => {
          target.skillActive = false;
          target.defense = target.originalDefense;
          if (targetEl) targetEl.style.border = '';
        }, target.skillDuration);
      }
    }
  }

  // Apply damage reduction for Super Team protection
  if (target.superTeamProtected) {
    damage = Math.floor(damage * 0.3); // Reduce damage by 70% (same as defense x3 effect)
  }

  // Path1: heal when taking damage
  if (target.type === 'tank' && target.path === 'path1' && damage > 0) {
    const now = Date.now();
    if (!target.ironWillCooldown || now - target.ironWillCooldown >= 7000) { // 7 second cooldown
      const healAmount = Math.floor(Math.random() * 51);
      target.health = Math.min(target.maxHealth, target.health + healAmount);
      target.ironWillCooldown = now;
      
      const targetEl = document.getElementById(target.id);
      if (targetEl) {
        const healthBar = targetEl.querySelector('.health-bar-fill');
        healthBar.style.width = `${(target.health / target.maxHealth) * 100}%`;
        // Visual effect for Path1 healing
        targetEl.style.filter = 'brightness(1.3)';
        setTimeout(() => targetEl.style.filter = '', 300);
      }
      showDamageText(target.x, target.y, healAmount, 'heal');
    }
  }

  // Silver Knight self-healing when taking damage
  if (target.type === 'knight' && target.path === 'path1' && damage > 0) {
    const healAmount = Math.floor(damage * 0.80); // 80% of damge got from enemy
    target.attack += 0.1;
    target.speed += 0.1;
    target.health = Math.min(target.maxHealth, target.health + healAmount);
    showDamageText(target.x, target.y, healAmount, 'heal');
    
    const targetEl = document.getElementById(target.id);
    if (targetEl) {
      const healthBar = targetEl.querySelector('.health-bar-fill');
      healthBar.style.width = `${(target.health / target.maxHealth) * 100}%`;
      targetEl.style.filter = 'brightness(1.3)';
      setTimeout(() => targetEl.style.filter = '', 300);
    }
  }

  target.health -= damage;
  const targetEl = document.getElementById(target.id);

  // Apply hit effect to target when taking damage
  if (damage > 0) {
    createHitEffect(target.id);
  }

  // Apply lifesteal for all characters
  if (attacker && damage > 0) {
    let lifestealAmount = 0;

    // Prevent vampire vs vampire lifesteal and cyborg lifesteal
    const isVampireVsVampire = attacker.type === 'vampire' && target.type === 'vampire';
    const isCyborgTarget = target.type === 'cyborg';

    // Apply lifesteal if present
    if (attacker.lifesteal && !isVampireVsVampire && !isCyborgTarget) {
      // Lifesteal calculation - 15% of damage for vampires, otherwise use unit's value
      const lifestealMultiplier = attacker.type === 'vampire' ? 0.12 : attacker.lifesteal;

      // Use Math.ceil and ensure a minimum of 2 points of lifesteal for significant damage
      if (damage >= 3) {
        lifestealAmount = Math.max(1, Math.ceil(damage * lifestealMultiplier));
      } else {
        lifestealAmount = Math.ceil(damage * lifestealMultiplier);
      }

      console.log(`Calculated lifesteal amount: ${lifestealAmount} (${damage} * ${lifestealMultiplier})`);
    }

    // Add Berserk skill lifesteal bonus if active
    if (attacker.type === 'berserk' && attacker.skillActive && attacker.berserkLifestealBonus) {
      lifestealAmount += attacker.berserkLifestealBonus;
      console.log(`Added Berserk skill lifesteal: ${attacker.berserkLifestealBonus}, total: ${lifestealAmount}`);
      // Reset the bonus so it's not applied multiple times
      attacker.berserkLifestealBonus = 0;
    }

    // Berserk drain attack - 100% damage as lifesteal
    if (attacker.type === 'berserk' && attacker.nextAttackDrain) {
      lifestealAmount = damage; // 100% of damage dealt
      attacker.nextAttackDrain = false; // Reset the drain flag

      // Visual effect for drain attack
      const attackerEl = document.getElementById(attacker.id);
      if (attackerEl) {
        attackerEl.style.border = '';
        attackerEl.style.boxShadow = '0 0 30px #00ff00';
        setTimeout(() => {
          if (attackerEl) attackerEl.style.boxShadow = '';
        }, 1000);
      }
      showDamageText(attacker.x, attacker.y, damage.toFixed(1), 'heal');
    }

    // Enhanced lifesteal for Vampire character - special drain effect
    if (attacker.type === 'vampire' && !isVampireVsVampire && !isCyborgTarget) {
      // Vampire drain effect - steals a percentage of target's current health
      const targetCurrentHealth = target.health;
      const drainPercentage = 0.025; // 2.5% drain

      // Calculate drain amount based on target's current health
      let vampireDrainAmount = Math.ceil(targetCurrentHealth * drainPercentage);

      // Ensure minimum drain based on damage dealt
      const minimumDrain = Math.max(1, Math.ceil(damage * 0.05)); // At least 5% of damage or 1 health

      // Use the higher value between percentage drain and minimum drain
      vampireDrainAmount = Math.max(vampireDrainAmount, minimumDrain);

      // Cap total lifesteal per hit to 12 HP
      vampireDrainAmount = Math.min(vampireDrainAmount, 12);

      // Add the drain amount to lifesteal
      lifestealAmount += vampireDrainAmount;

      console.log(`Vampire drain: ${vampireDrainAmount} (${drainPercentage * 100}% of target's ${targetCurrentHealth} health or ${minimumDrain} minimum), total: ${lifestealAmount}`);
    }

    if (lifestealAmount > 0) {
      attacker.health = Math.min(attacker.maxHealth, attacker.health + lifestealAmount);
      const attackerEl = document.getElementById(attacker.id);
      if (attackerEl) {
        const healthBar = attackerEl.querySelector('.health-bar-fill');
        healthBar.style.width = `${(attacker.health / attacker.maxHealth) * 100}%`;
        // Visual effect for lifesteal
        attackerEl.style.filter = 'brightness(1.2)';
        setTimeout(() => attackerEl.style.filter = '', 200);
      }
      showDamageText(attacker.x, attacker.y, lifestealAmount, 'heal');
    }
  }

  // Apply counter damage if the target has the counter_damage effect
  if (target.counterDamage && damage > 0 && attacker && attacker.health > 0) {
    const counterDamage = Math.max(1, Math.floor(damage * target.counterDamage));
    attacker.health = Math.max(1, attacker.health - counterDamage); // Attacker cannot die from counter
    createHitEffect(attacker.id, true);
    showDamageText(attacker.x, attacker.y, counterDamage, 'counter');
    const attackerEl = document.getElementById(attacker.id);
    if (attackerEl) {
      const healthBar = attackerEl.querySelector('.health-bar-fill');
      healthBar.style.width = `${(attacker.health / attacker.maxHealth) * 100}%`;
    }
  }

  if (target.health <= 0) {
    handleUnitDeath(attacker, target);
    return;
  } else if (targetEl) {
    const healthBar = targetEl.querySelector('.health-bar-fill');
    healthBar.style.width = `${(target.health / target.maxHealth) * 100}%`;
  }
}

function soulHarvest(attacker, target) {
  // Calculate soul harvest amount
  const healAmount = Math.floor(target.maxHealth * 0.15);

  // Apply additional healing from reaperBonus if present
  const totalHealAmount = attacker.reaperBonus ?
    Math.floor(healAmount * (1 + attacker.reaperBonus)) :
    healAmount;

  attacker.health = Math.min(attacker.maxHealth, attacker.health + totalHealAmount);
  const attackerEl = document.getElementById(attacker.id);
  if (attackerEl) {
    const healthBar = attackerEl.querySelector('.health-bar-fill');
    healthBar.style.width = `${(attacker.health / attacker.maxHealth) * 100}%`;
  }
  showDamageText(attacker.x, attacker.y, totalHealAmount, 'heal');
}

function respawnGhost(unit) {
  unit.form = 'ghost';
  // Apply ghost form stats
  unit.maxHealth *= 1.5;
  unit.health = unit.maxHealth * 1.2;
  unit.attack *= 1.5;
  unit.defense *= 1.3;
  unit.speed *= 1.8;
  unit.critChance *= 1.5;
  unit.accuracy *= 1.5;
  unit.attackSpeed = 3;
  unit.attackCooldown = 1000 / unit.attackSpeed;

  const unitEl = document.getElementById(unit.id);
  if (unitEl) {
    unitEl.classList.add('ghost-form');
    unitEl.innerHTML = `👻<div class="health-bar"><div class="health-bar-fill" style="width: 100%"></div></div>`;
  }

  // GHOST BUFF: Stun all enemy units for 5 seconds when respawning
  const enemyUnits = unit.player === 1 ?
    [...player2Units].filter(u => u && u.health > 0) :
    [...player1Units].filter(u => u && u.health > 0);

  enemyUnits.forEach(target => {
    target.stunned = true;
    const targetEl = document.getElementById(target.id);
    if (targetEl) {
      const stunEffect = document.createElement('div');
      stunEffect.className = 'stun-effect';
      stunEffect.textContent = '💫';
      stunEffect.style.position = 'absolute';
      stunEffect.style.top = '-25px';
      stunEffect.style.left = '50%';
      stunEffect.style.transform = 'translateX(-50%)';
      stunEffect.style.fontSize = '20px';
      stunEffect.style.zIndex = '10';
      stunEffect.id = `${target.id}-ghost-stun`;
      targetEl.appendChild(stunEffect);
    }
  });

  // Remove stun after 5 seconds
  setTimeout(() => {
    enemyUnits.forEach(target => {
      if (target) {
        target.stunned = false;
        const targetEl = document.getElementById(target.id);
        if (targetEl) {
          const stunEffect = document.getElementById(`${target.id}-ghost-stun`);
          if (stunEffect) stunEffect.remove();
        }
      }
    });
  }, 5000);

  // Play radio1.mp3 sound
  try {
    let audio = document.getElementById('ghost-respawn-audio');
    if (!audio) {
      audio = document.createElement('audio');
      audio.id = 'ghost-respawn-audio';
      audio.src = 'radio1.mp3';
      audio.preload = 'auto';
      document.body.appendChild(audio);
    }
    audio.currentTime = 0;
    audio.play().catch(e => console.log('Audio play failed:', e));
  } catch (e) {
    console.log('Audio error:', e);
  }

  // Visual effect for ghost respawn
  if (unitEl) {
    unitEl.style.boxShadow = '0 0 100px #800080';
    setTimeout(() => {
      if (unitEl) unitEl.style.boxShadow = '';
    }, 2000);
  }
}

function tryTeleport(unit) {
  if (unit.type === 'tank') return;

  if (typeof unit.teleportCount === 'undefined') unit.teleportCount = 0;
  const thresholds = [0.5, 0.3];
  if (typeof unit.passedThresholds === 'undefined') unit.passedThresholds = [];

  let currentHealthPercent = unit.health / unit.maxHealth;
  let shouldTeleport = false;
  let thresholdIndex = -1;

  for (let i = 0; i < thresholds.length; i++) {
    if (!unit.passedThresholds.includes(i)) {
      if (currentHealthPercent < thresholds[i]) {
        shouldTeleport = true;
        thresholdIndex = i;
        break;
      }
    }
  }
  // Allow vampire and berserk to reset teleport thresholds when health recovers
  if ((unit.type === 'vampire' || unit.type === 'berserk') && unit.passedThresholds.length > 0) {
    for (let i = 0; i < unit.passedThresholds.length; i++) {
      let idx = unit.passedThresholds[i];
      if (currentHealthPercent > thresholds[idx] + 0.05) {
        unit.passedThresholds.splice(i, 1);
        i--;
      }
    }
  }

  if (!shouldTeleport) return;

  unit.passedThresholds.push(thresholdIndex);
  unit.teleportCount++;

  const battlefield = document.getElementById('battlefield');
  const battlefieldWidth = battlefield.clientWidth;
  const battlefieldHeight = battlefield.clientHeight;
  const unitSize = 40;

  const angle = Math.random() * 2 * Math.PI;
  const distance = 100;

  let newX = unit.x + Math.cos(angle) * distance;
  let newY = unit.y + Math.sin(angle) * distance;

  newX = Math.max(0, Math.min(newX, battlefieldWidth - unitSize));
  newY = Math.max(0, Math.min(newY, battlefieldHeight - unitSize));

  unit.x = newX;
  unit.y = newY;
  unit.targetX = newX;
  unit.targetY = newY;

  const unitEl = document.getElementById(unit.id);
  if (unitEl) {
    unitEl.style.left = `${unit.x}px`;
    unitEl.style.top = `${unit.y}px`;
  }
}



function updateAI() {
  const allUnits = [...player1Units, ...player2Units, ...player1Clones, ...player2Clones].filter(unit => unit && (unit.health > 0 || unit.isRecharging));

  // Check passive abilities first (no cooldown restrictions)
  for (const unit of allUnits) checkPassiveAbilities(unit);

  for (const unit of allUnits) tryTeleport(unit);
  for (const unit of allUnits) {
    const allies = unit.player === 1 ? [...player1Units, ...player1Clones] : [...player2Units, ...player2Clones];
    const enemies = unit.player === 1 ? [...player2Units, ...player2Clones] : [...player1Units, ...player1Clones];
    tryActivateSkill(unit, allies, enemies);
  }

  for (const unit of allUnits) {
    // Skip stunned, frozen, or recharging units
    if (unit.stunned || unit.frozen || unit.isRecharging || unit.sleeping) {
      continue;
    }
    
    const allies = unit.player === 1 ? [...player1Units, ...player1Clones] : [...player2Units, ...player2Clones];
    const enemies = unit.player === 1 ? [...player2Units, ...player2Clones] : [...player1Units, ...player1Clones];
    const target = findTarget(unit, enemies);

    if (target) {
      const attackRange = getAttackRange(unit);
      const distance = getDistance(unit.x, unit.y, target.x, target.y);

      if (unit.range > 1) {
        const nearestTank = findNearestTank(unit, allies);
        if (nearestTank) {
          const coverPos = calculateCoverPosition(unit, nearestTank, target);
          unit.targetX = coverPos.x;
          unit.targetY = coverPos.y;
        } else {
          if (distance < attackRange * 0.8) {
            const angle = Math.atan2(unit.y - target.y, unit.x - target.x);
            unit.targetX = target.x + Math.cos(angle) * attackRange;
            unit.targetY = target.y + Math.sin(angle) * attackRange;
          } else if (distance > attackRange * 0.9) {
            const angle = Math.atan2(target.y - unit.y, target.x - unit.x);
            unit.targetX = target.x - Math.cos(angle) * (attackRange * 0.85);
            unit.targetY = target.y - Math.sin(angle) * (attackRange * 0.85);
          }
        }
      } else {
        if (distance > attackRange) {
          const angle = Math.atan2(target.y - unit.y, target.x - unit.x);
          unit.targetX = target.x - Math.cos(angle) * (attackRange - 5);
          unit.targetY = target.y - Math.sin(angle) * (attackRange - 5);
        } else {
          unit.targetX = unit.x;
          unit.targetY = unit.y;
        }
      }

      if (distance <= attackRange) attack(unit, target);
    }
  }
}

function gameLoop() {
  if (!battleStarted) return;
  const now = Date.now();
  const elapsed = now - lastUpdate;

  if (elapsed > frameTime) {
    updateAI();

    const allUnits = [...player1Units, ...player2Units, ...player1Clones, ...player2Clones].filter(unit => unit && (unit.health > 0 || unit.isRecharging));
    
    // Clone cleanup
    const now = Date.now();
    player1Clones = player1Clones.filter(clone => {
        if (clone.health <= 0 || now >= clone.disappearTime) {
            const el = document.getElementById(clone.id);
            if (el) el.remove();
            return false;
        }
        return true;
    });
    player2Clones = player2Clones.filter(clone => {
        if (clone.health <= 0 || now >= clone.disappearTime) {
            const el = document.getElementById(clone.id);
            if (el) el.remove();
            return false;
        }
        return true;
    });

    for (const unit of allUnits) {
      updateUnitPosition(unit);

      if (unit.bleeding && unit.bleeding.active) {
        const bleedElapsed = now - unit.bleeding.lastTick;
        const damage = unit.bleeding.damagePerSecond * (bleedElapsed / 1000);
        unit.health -= damage;
        unit.bleeding.duration -= bleedElapsed;
        unit.bleeding.lastTick = now;
        if (unit.bleeding.duration <= 0) {
          unit.bleeding.active = false;
          const unitEl = document.getElementById(unit.id);
          if (unitEl) {
            const healthBar = unitEl.querySelector('.health-bar-fill');
            healthBar.classList.remove('health-bar-bleeding');
          }
        }
        const unitEl = document.getElementById(unit.id);
        if (unitEl) {
          const healthBar = unitEl.querySelector('.health-bar-fill');
          healthBar.style.width = `${(unit.health / unit.maxHealth) * 100}%`;
        }
        if (unit.health <= 0) {
          // Make sure to properly remove the unit
          handleUnitDeath(null, unit);
        }
      }
    }

    const player1Alive = player1Units.some(u => u && (u.health > 0 || u.isRecharging));
    const player2Alive = player2Units.some(u => u && (u.health > 0 || u.isRecharging));

    if (!player1Alive || !player2Alive) {
      battleStarted = false;
      gameEnded = true;
      updateStatus();
      document.getElementById('startButton').style.display = 'none';
      document.getElementById('playAgainButton').style.display = 'block';
      return;
    }

    lastUpdate = now;
  }
  requestAnimationFrame(gameLoop);
}

document.getElementById('startButton').onclick = () => {
  if (player1Units.filter(u => u).length !=0 && player2Units.filter(u => u).length !=0) {
    battleStarted = true;
    document.getElementById('startButton').style.display = 'none';
    document.getElementById('status').textContent = 'Battle in progress!';

    // Handle Ninja Assassin skill at the start of the battle
    const allUnits = [...player1Units, ...player2Units].filter(u => u);
    for (const unit of allUnits) {
      if (unit.type === 'ninja' && unit.path === 'path2') {
        handleAssassinSkill(unit);
      }
    }

    // If this is a bot mode game, set the cooldown now that the battle is actually starting
    if (gameState.currentMode === 'bot') {
      gameState.lastBotGame = new Date().toISOString();
      saveGameState();
      updateBotCooldown();
    }

    // Track battle start
    trackGameAction('Battle Started', {
      mode: gameState.currentMode,
      player1Units: player1Units.filter(u => u).map(u => u.type),
      player2Units: player2Units.filter(u => u).map(u => u.type)
    });

    // Disable character selection for both players ONLY after pressing start in infinite mode
    if (gameState.currentMode === 'infinite') {
      setTimeout(() => {
        const p1Chars = document.getElementById('player1-characters');
        const p2Chars = document.getElementById('player2-characters');
        if (p1Chars) Array.from(p1Chars.children).forEach(el => el.style.pointerEvents = 'none');
        if (p2Chars) Array.from(p2Chars.children).forEach(el => el.style.pointerEvents = 'none');
      }, 100);
    }

    gameLoop();
  }
};

function resetGame() {
  player1Units = [];
  player2Units = [];
  player1Slots = Array.from({length: gameState.player1SlotCount}, (_, i) => i);
  if (gameState.currentMode === 'infinite') {
    player2Slots = [0, 1, 2, 3];
  } else {
    player2Slots = [0, 1, 2];
  }
  player1Clones = [];
  player2Clones = [];
  battleStarted = false;
  gameEnded = false;
  lastUpdate = Date.now();

  const battlefield = document.getElementById('battlefield');
  const units = battlefield.querySelectorAll('.unit');
  units.forEach(unit => unit.remove());

  // Hide result screen
  document.getElementById('resultScreen').style.display = 'none';

  // Recreate character selection based on game mode
  createCharacterSelection();

  updateStatus();
  document.getElementById('status').textContent = `Player 1: 0/${gameState.player1SlotCount} units | Player 2: 0/3 units`;
}

// Override the game loop to handle game results
const originalGameLoop = gameLoop;
gameLoop = function() {
  if (!battleStarted) return;
  const now = Date.now();
  const elapsed = now - lastUpdate;

  if (elapsed > frameTime) {
    updateAI();

    const allUnits = [...player1Units, ...player2Units, ...player1Clones, ...player2Clones].filter(unit => unit && (unit.health > 0 || unit.isRecharging));
    
    // Clone cleanup
    const now = Date.now();
    player1Clones = player1Clones.filter(clone => {
        if (clone.health <= 0 || now >= clone.disappearTime) {
            const el = document.getElementById(clone.id);
            if (el) el.remove();
            return false;
        }
        return true;
    });
    player2Clones = player2Clones.filter(clone => {
        if (clone.health <= 0 || now >= clone.disappearTime) {
            const el = document.getElementById(clone.id);
            if (el) el.remove();
            return false;
        }
        return true;
    });

    for (const unit of allUnits) {
      updateUnitPosition(unit);

      if (unit.bleeding && unit.bleeding.active) {
        const bleedElapsed = now - unit.bleeding.lastTick;
        const damage = unit.bleeding.damagePerSecond * (bleedElapsed / 1000);
        unit.health -= damage;
        unit.bleeding.duration -= bleedElapsed;
        unit.bleeding.lastTick = now;
        if (unit.bleeding.duration <= 0) {
          unit.bleeding.active = false;
          const unitEl = document.getElementById(unit.id);
          if (unitEl) {
            const healthBar = unitEl.querySelector('.health-bar-fill');
            healthBar.classList.remove('health-bar-bleeding');
          }
        }
        const unitEl = document.getElementById(unit.id);
        if (unitEl) {
          const healthBar = unitEl.querySelector('.health-bar-fill');
          healthBar.style.width = `${(unit.health / unit.maxHealth) * 100}%`;
        }
        if (unit.health <= 0) {
          // Make sure to properly remove the unit
          handleUnitDeath(null, unit);
        }
      }
    }

    const player1Alive = player1Units.some(u => u && (u.health > 0 || u.isRecharging));
    const player2Alive = player2Units.some(u => u && (u.health > 0 || u.isRecharging));

    if (!player1Alive || !player2Alive) {
      battleStarted = false;
      gameEnded = true;

      // Handle game result
      handleGameResult(player1Alive);
      // ลบทุกอย่างออกจาก battlefield
      clearBattlefieldEffects();

      return;
    }

    lastUpdate = now;
  }
  requestAnimationFrame(gameLoop);
};

document.addEventListener('keydown', function(event) {
  if (event.ctrlKey && event.shiftKey && (event.key === 'I' || event.key === 'J')) event.preventDefault();
  if (event.ctrlKey && (event.key === 'u' || event.key === 'c' || event.key === 'x' || event.key === 's' || event.key === 'a')) event.preventDefault();
  if ((event.ctrlKey || event.metaKey) && (event.key === '+' || event.key === '-' || event.key === '0')) event.preventDefault();
});

document.addEventListener('contextmenu', function(event) {
  event.preventDefault();
});

// Initialize the game when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  initGame();
});

// Character upgrade system
function getUpgradeCost(characterType, upgradeLevel) {
  const baseCost = 60;
  return baseCost + (upgradeLevel * 40);
}

function getUpgradeStats(characterType, upgradeLevel) {
  const baseStats = characters.find(c => c.type === characterType);
  if (!baseStats) return null;

  const upgradeMultiplier = 1 + (upgradeLevel * 0.1); // 10% increase per level
  
  return {
    hp: Math.floor(baseStats.hp * upgradeMultiplier),
    attack: Math.floor(baseStats.attack * upgradeMultiplier),
    defense: Math.floor(baseStats.defense * upgradeMultiplier),
    speed: Math.floor(baseStats.speed * upgradeMultiplier),
    critChance: Math.floor(baseStats.critChance * upgradeMultiplier),
    accuracy: Math.floor(baseStats.accuracy * upgradeMultiplier),
    attackSpeed: baseStats.attackSpeed * upgradeMultiplier
  };
}

function upgradeCharacter(characterType) {
  const upgradeData = gameState.characterUpgrades[characterType] || { level: 0 };
  const currentLevel = upgradeData.level;

  // Special case for Berserk reaching level 3
  if (characterType === 'berserk' && currentLevel === 2) {
    const upgradeCost = getUpgradeCost(characterType, currentLevel);
    if (gameState.coins < upgradeCost) {
      showNotification('Not enough coins for upgrade!', 'error');
      return;
    }
    
    showModal('Choose Specialization for Berserk',
      `path1: เพิ่มพลังป้องกันขึ้นหลายเท่าเมื่อเข้าสู่โหมด Berserk และเพิ่ม HP ถาวร 10% กับ DEF 15% + Lifesteal 15%\n\npath2: เป็นอมตะ 2 วินาทีเมื่อเข้าสู่โหมด Berserk และเพิ่ม ATK ถาวร 12% + Lifesteal 20%`,
      [
        { text: 'path1', action: () => setBerserkPath('path1') },
        { text: 'path2', action: () => setBerserkPath('path2') },
        { text: 'ยกเลิก', secondary: true }
      ]
    );
    return;
  }

  // Special case for Archer reaching level 3
  if (characterType === 'archer' && currentLevel === 2) {
    const upgradeCost = getUpgradeCost(characterType, currentLevel);
    if (gameState.coins < upgradeCost) {
      showNotification('Not enough coins for upgrade!', 'error');
      return;
    }
    
    showModal('Choose Specialization for Archer',
      `path1: เพิ่มระยะยิงและยิงธนูสองดอกพร้อมกันเมื่อใช้สกิล\n\npath2: เพิ่มดาเมจพื้นฐาน เเละ เมื่อกำจัดศัตรูได้จะยิงธนู 5 ดอกใส่ศัตรูที่ใกล้ที่สุดโดยอัตโนมัติ`,
      [
        { text: 'path1', action: () => setArcherPath('path1') },
        { text: 'path2', action: () => setArcherPath('path2') },
        { text: 'ยกเลิก', secondary: true }
      ]
    );
    return;
  }

  // Special case for Tank reaching level 3
  if (characterType === 'tank' && currentLevel === 2) {
    const upgradeCost = getUpgradeCost(characterType, currentLevel);
    if (gameState.coins < upgradeCost) {
      showNotification('Not enough coins for upgrade!', 'error');
      return;
    }
    
    showModal('Choose Specialization for Tank',
      `path1: ฟื้นฟูตัวเอง สุ่มๆ 1-50 ของ hp ตัวเอง เมื่อโดนโจมตี (คูลดาวน์ 7 วิ)\n\npath2: เปิดโล่ให้ทุกคนในทีม เมื่อตนเอง โดนโจมตี`,
      [
        { text: 'path1', action: () => setTankPath('path1') },
        { text: 'path2', action: () => setTankPath('path2') },
        { text: 'ยกเลิก', secondary: true }
      ]
    );
    return;
  }

  // Special case for Mage reaching level 3
  if (characterType === 'mage' && currentLevel === 2) {
    const upgradeCost = getUpgradeCost(characterType, currentLevel);
    if (gameState.coins < upgradeCost) {
      showNotification('Not enough coins for upgrade!', 'error');
      return;
    }
    
    showModal('Choose Specialization for Mage',
      `path1: Meteor รุนแรงขึ้นและคูลดาวน์ไวขึ้น\n\npath2: Meteor จะเเช่เเข็ง ศัตรู 3 วินาที`,
      [
        { text: 'path1', action: () => setMagePath('path1') },
        { text: 'path2', action: () => setMagePath('path2') },
        { text: 'ยกเลิก', secondary: true }
      ]
    );
    return;
  }
  // Special case for Vampire reaching level 3
  if (characterType === 'vampire' && currentLevel === 2) {
    const upgradeCost = getUpgradeCost(characterType, currentLevel);
    if (gameState.coins < upgradeCost) {
      showNotification('Not enough coins for upgrade!', 'error');
      return;
    }
    showModal('Choose Specialization for Vampire',
      `path1: จะทำการ ลดเลือด ตัวเอง 60% เเละ โจมตีศัตรูที่ใกล้ที่สุด ตามเลือดที่ลดลง\n\npath2: ดูดเลือดแรงมาก และเมื่อกำจัดศัตรูได้จะระเบิดศัตรูเป็นวงกว้าง ดูดเลือดจากศัตรูรอบข้าง 20% ของ HP`,
      [
        { text: 'path1', action: () => setVampirePath('path1') },
        { text: 'path2', action: () => setVampirePath('path2') },
        { text: 'ยกเลิก', secondary: true }
      ]
    );
    return;
  }

  // Special case for Gunman reaching level 3
  if (characterType === 'gunman' && currentLevel === 2) {
    const upgradeCost = getUpgradeCost(characterType, currentLevel);
    if (gameState.coins < upgradeCost) {
      showNotification('Not enough coins for upgrade!', 'error');
      return;
    }
    showModal('Choose Specialization for Gunman',
      `path2: ยิงไกล เเละ ดาเมจสูง`,
      [
        { text: 'path2', action: () => setGunmanPath('path2') },
        { text: 'ยกเลิก', secondary: true }
      ]
    );
    return;
  }

  // Special case for Knight reaching level 3
  if (characterType === 'knight' && currentLevel === 2) {
    const upgradeCost = getUpgradeCost(characterType, currentLevel);
    if (gameState.coins < upgradeCost) {
      showNotification('Not enough coins for upgrade!', 'error');
      return;
    }
    showModal('Choose Specialization for Knight',
      `path1: ฟื้นฟูตัวเอง 80% ของดาเมจ ที่ได้รับ เเละ ทุกครั้งที่โจมตี จะเพิ่ม ค่า damage เเละ ความเร็วเคลื่อนที่ 0.1 หน่วย\n\npath2: หากตายจะมีโอกาส 40% ที่จะเกิดใหม่`,
      [
        { text: 'path1', action: () => setKnightPath('path1') },
        { text: 'path2', action: () => setKnightPath('path2') },
        { text: 'ยกเลิก', secondary: true }
      ]
    );
    return;
  }

  // Special case for Ninja reaching level 3
  if (characterType === 'ninja' && currentLevel === 2) {
    const upgradeCost = getUpgradeCost(characterType, currentLevel);
    if (gameState.coins < upgradeCost) {
      showNotification('Not enough coins for upgrade!', 'error');
      return;
    }
    showModal('Choose Specialization for Ninja',
      `Path 1: เเยกร่างออกมา 2 ตัว อยู่ได้นาน 7 วิ หรือ จนตาย\n\nPath 2: เมื่อเริ่มการต่อสู้ จะวาร์ปไปด้านหลังศัตรูตัวระยะไกล (ประเภทโจมตีไกล) และสร้างความเสียหาย ที่รุนเเรง แล้ววาร์ปกลับ`,
      [
        { text: 'Path 1', action: () => setNinjaPath('path1') },
        { text: 'Path 2', action: () => setNinjaPath('path2') },
        { text: 'ยกเลิก', secondary: true }
      ]
    );
    return;
  }

  if (currentLevel >= 5) {
    showNotification(`${characterType} is already at maximum level!`, 'warning');
    return;
  }
  
  const upgradeCost = getUpgradeCost(characterType, currentLevel);
  
  if (gameState.coins >= upgradeCost) {
    gameState.coins -= upgradeCost;
    gameState.characterUpgrades[characterType] = {
      ...upgradeData,
      level: currentLevel + 1
    };
    saveGameState();
    updateUI();
    
    if (gameState.currentScreen === 'upgrade') {
      createUpgradeItems();
    }
    
    showNotification(`Upgraded ${characterType} to level ${currentLevel + 1}!`, 'success');
  } else {
    showNotification('Not enough coins for upgrade!', 'error');
  }
}

function getCharacterWithUpgrades(characterType, ignoreUpgrade = false) {
  const baseCharacter = characters.find(c => c.type === characterType);
  if (!baseCharacter) return null;

  // ถ้า ignoreUpgrade เป็น true ให้คืนค่า base stat ทันที
  if (ignoreUpgrade) return { ...baseCharacter };

  let finalStats = { ...baseCharacter };

  const upgradeData = gameState.characterUpgrades[characterType];
  if (!upgradeData) return finalStats;

  const upgradeLevel = upgradeData.level || 0;

  // Create a temporary base to apply path modifications
  let pathModifiedBase = { ...baseCharacter };
  if (characterType === 'berserk' && upgradeData.path) {
    if (upgradeData.path === 'path1') {
      pathModifiedBase.hp = Math.floor(pathModifiedBase.hp * 1.10); // Nerfed from 1.15
      pathModifiedBase.defense = Math.floor(pathModifiedBase.defense * 1.15); // Nerfed from 1.25
    } else if (upgradeData.path === 'path2') {
      pathModifiedBase.attack = Math.floor(pathModifiedBase.attack * 1.12); // Nerfed from 1.20
    }
  }
  
  if (characterType === 'archer' && upgradeData.path) {
    if (upgradeData.path === 'path1') {
      pathModifiedBase.range = Math.floor(pathModifiedBase.range * 1.5); // Nerfed from 2
      pathModifiedBase.attack = Math.floor(pathModifiedBase.attack * 1.08); // Nerfed from 1.12
    } else if (upgradeData.path === 'path2') {
      pathModifiedBase.hp = Math.floor(pathModifiedBase.hp * 1.2); // Nerfed from 1.3
      pathModifiedBase.defense = Math.floor(pathModifiedBase.defense * 1.2); // Nerfed from 1.4
      pathModifiedBase.speed = Math.floor(pathModifiedBase.speed * 1.1); // Nerfed from 1.3
      pathModifiedBase.attackSpeed = pathModifiedBase.attackSpeed * 1.05; // Nerfed from 1.1
      pathModifiedBase.attack = Math.floor(pathModifiedBase.attack * 0.9); // Nerfed from 0.8
    }
  }

  if (characterType === 'tank' && upgradeData.path) {
    if (upgradeData.path === 'path1') {
      pathModifiedBase.hp = Math.floor(pathModifiedBase.hp * 1.25); // Nerfed from 1.6
      pathModifiedBase.defense = Math.floor(pathModifiedBase.defense * 1.25); // Nerfed from 1.5
    } else if (upgradeData.path === 'path2') {
      pathModifiedBase.hp = Math.floor(pathModifiedBase.hp * 1.25); // Nerfed from 1.6
      pathModifiedBase.defense = Math.floor(pathModifiedBase.defense * 1.2); // Nerfed from 1.4
      pathModifiedBase.attack = Math.floor(pathModifiedBase.attack * 1.15); // Nerfed from 1.5
    }
  }

  if (characterType === 'mage' && upgradeData.path) {
    if (upgradeData.path === 'path1') {
      pathModifiedBase.attack = Math.floor(pathModifiedBase.attack * 1.15); // Nerfed from 1.25
      pathModifiedBase.attackSpeed = pathModifiedBase.attackSpeed * 1.1; // Nerfed from 1.2
      pathModifiedBase.critChance = Math.floor(pathModifiedBase.critChance * 1.15); // Nerfed from 1.3
    } else if (upgradeData.path === 'path2') {
      pathModifiedBase.attack = Math.floor(pathModifiedBase.attack * 1.1); // Nerfed from 1.2
      pathModifiedBase.critChance = Math.floor(pathModifiedBase.critChance * 1.2); // Nerfed from 1.4
      pathModifiedBase.accuracy = Math.floor(pathModifiedBase.accuracy * 1.05); // Nerfed from 1.1
    }
  }

  if (characterType === 'murder') {
    pathModifiedBase.attack = Math.floor(pathModifiedBase.attack * 1.15);
    pathModifiedBase.attackSpeed = Math.floor(pathModifiedBase.attackSpeed * 1.1);
    pathModifiedBase.critChance = Math.floor(pathModifiedBase.critChance * 1.1); // 30% crit chance increase
  }
  
  if (characterType === 'ghost') {
    pathModifiedBase.attack = Math.floor(pathModifiedBase.attack * 1.15);
    pathModifiedBase.attackSpeed = Math.floor(pathModifiedBase.attackSpeed * 1.25);
    pathModifiedBase.maxHealth = Math.floor(pathModifiedBase.maxHealth * 1.1);
  }

  if (characterType === 'cyborg') {
    pathModifiedBase.attack = Math.floor(pathModifiedBase.attack * 1.15);
    pathModifiedBase.attackSpeed = Math.floor(pathModifiedBase.attackSpeed * 1.25);
    pathModifiedBase.maxHealth = Math.floor(pathModifiedBase.maxHealth * 1.1);
  }

  if (characterType === 'vampire' && upgradeData.path) {
    if (upgradeData.path === 'path1') {
      pathModifiedBase.hp = Math.floor(pathModifiedBase.hp * 1.15); // Nerfed from 1.3
      pathModifiedBase.defense = Math.floor(pathModifiedBase.defense * 1.15); // Nerfed from 1.3
      pathModifiedBase.lifesteal = Math.min((pathModifiedBase.lifesteal || 0.06) + 0.06, 0.20); // Nerfed from +0.10, cap 20%
      if (upgradeLevel >= 3) {
        pathModifiedBase.hasImmortal = true;
      }
    } else if (upgradeData.path === 'path2') {
      pathModifiedBase.attack = Math.floor(pathModifiedBase.attack * 1.1); // Nerfed from 1.2
      pathModifiedBase.lifesteal = Math.min((pathModifiedBase.lifesteal || 0.06) + 0.10, 0.20); // Nerfed from +0.20, cap 20%
      if (upgradeLevel >= 3) {
        pathModifiedBase.hasStrongAoE = true;
      }
    }
  }

  if (characterType === 'gunman' && upgradeData.path) {
    if (upgradeData.path === 'path1') {
      // ลูกองเลย - ยิงหลายนัดในระยะ 2
      pathModifiedBase.attack = Math.floor(pathModifiedBase.attack * 1.3);
      pathModifiedBase.attackSpeed = pathModifiedBase.attackSpeed * 1.5;
      pathModifiedBase.multiShot = true; // Flag for multiple shots
    } else if (upgradeData.path === 'path2') {
      // ยิงไกลและแรง
      pathModifiedBase.range = Math.floor(pathModifiedBase.range * 2.5);
      pathModifiedBase.attack = Math.floor(pathModifiedBase.attack * 1.2);
      pathModifiedBase.longRange = true; // Flag for long range
    }
  }

  if (characterType === 'knight' && upgradeData.path) {
    if (upgradeData.path === 'path1') {
      // Silver Knight - อึดมาก
      pathModifiedBase.hp = Math.floor(pathModifiedBase.hp * 1.5);
      pathModifiedBase.defense = Math.floor(pathModifiedBase.defense * 1.8);
      pathModifiedBase.attack = Math.floor(pathModifiedBase.attack * 1.2);
      pathModifiedBase.silverKnight = true; // Flag for self-healing
    } else if (upgradeData.path === 'path2') {
      // Dark Knight - แรงมาก
      pathModifiedBase.attack = Math.floor(pathModifiedBase.attack * 1.6);
      pathModifiedBase.critChance = (pathModifiedBase.critChance || 12) + 20;
      pathModifiedBase.attackSpeed = pathModifiedBase.attackSpeed * 1.3;
      pathModifiedBase.darkKnight = true; // Flag for revival
    }
  }

  if (characterType === 'ninja' && upgradeData.path) {
    if (upgradeData.path === 'path1') {
      // Clone path: +30% attack speed
      pathModifiedBase.attackSpeed = pathModifiedBase.attackSpeed * 1.3;
    } else if (upgradeData.path === 'path2') {
      // Assassin path: +35% attack
      pathModifiedBase.attack = Math.floor(pathModifiedBase.attack * 1.35);
    }
  }

  // Add explicit logic for reaper (if needed, currently just a placeholder for future upgrades)
  if (characterType === 'reaper') {
    // Add a base stat bonus for reaper
    pathModifiedBase.hp = Math.floor(pathModifiedBase.hp * 1.1);
    pathModifiedBase.attack = Math.floor(pathModifiedBase.attack * 1.1);
    pathModifiedBase.defense = Math.floor(pathModifiedBase.defense * 1.1);
  }

  // Add explicit logic for tungtung (if needed, currently just a placeholder for future upgrades)
  if (characterType === 'tungtung') {
    // Add a base stat bonus for tungtung
    pathModifiedBase.hp = Math.floor(pathModifiedBase.hp * 1.20);
    pathModifiedBase.attack = Math.floor(pathModifiedBase.attack * 1.50);
    pathModifiedBase.defense = Math.floor(pathModifiedBase.defense * 1.20);
  }

  // If there are level upgrades, calculate bonuses from the true original stats
  if (upgradeLevel > 0) {
      const levelBonusHp = Math.floor(baseCharacter.hp * (0.01 * upgradeLevel));
      const levelBonusAtk = Math.floor(baseCharacter.attack * (0.01 * upgradeLevel));
      const levelBonusDef = Math.floor(baseCharacter.defense * (0.01 * upgradeLevel));
      const levelBonusSpd = Math.floor(baseCharacter.speed * (0.01 * upgradeLevel));
      const levelBonusCrit = Math.floor(baseCharacter.critChance * (0.01 * upgradeLevel));
      const levelBonusAcc = Math.floor(baseCharacter.accuracy * (0.01 * upgradeLevel));
      const levelBonusAtkSpd = baseCharacter.attackSpeed * (0.01 * upgradeLevel);
    
      // Add bonuses to the path-modified stats
      finalStats = {
          ...pathModifiedBase,
          hp: pathModifiedBase.hp + levelBonusHp,
          attack: pathModifiedBase.attack + levelBonusAtk,
          defense: pathModifiedBase.defense + levelBonusDef,
          speed: pathModifiedBase.speed + levelBonusSpd,
          critChance: pathModifiedBase.critChance + levelBonusCrit,
          accuracy: pathModifiedBase.accuracy + levelBonusAcc,
          attackSpeed: pathModifiedBase.attackSpeed + levelBonusAtkSpd,
      };
  } else {
    // If no level upgrades, just use the path-modified stats
      finalStats = pathModifiedBase;
  }
  
  return finalStats;
}

// Create upgrade items
function createUpgradeItems() {
  const upgradeItems = document.getElementById('upgradeItems');
  upgradeItems.innerHTML = '';

  // Only show unlocked characters for upgrade
  gameState.unlockedCharacters.forEach(charType => {
    const baseChar = characters.find(c => c.type === charType);
    if (!baseChar) return;

    const upgradeData = gameState.characterUpgrades[charType] || { level: 0 };
    const currentLevel = upgradeData.level;
    const upgradeCost = getUpgradeCost(charType, currentLevel);
    const isMaxLevel = currentLevel >= 5;
    const canAfford = gameState.coins >= upgradeCost && !isMaxLevel;

    const upgradeItem = document.createElement('div');
    upgradeItem.className = 'upgrade-item';
    upgradeItem.dataset.type = 'character';
    upgradeItem.dataset.id = charType;

    const itemIcon = document.createElement('div');
    itemIcon.className = 'upgrade-item-icon';
    if (charType === 'tungtung') {
      itemIcon.innerHTML = `<img src="brainrot1.png" alt="tungtung" style="width:32px;height:32px;">`;
    } else {
      itemIcon.textContent = baseChar.name;
    }

    const itemName = document.createElement('div');
    itemName.className = 'upgrade-item-name';
    itemName.textContent = charType;
    if (upgradeData.path) {
      if (charType === 'mage') {
        const pathDisplay = upgradeData.path === 'path1' ? 'Path 1' : 'Path 2';
        itemName.textContent += ` (${pathDisplay})`;
      } else if (charType === 'berserk') {
        const pathDisplay = upgradeData.path === 'path1' ? 'Path 1' : 'Path 2';
        itemName.textContent += ` (${pathDisplay})`;
      } else if (charType === 'archer') {
        const pathDisplay = upgradeData.path === 'path1' ? 'Path 1' : 'Path 2';
        itemName.textContent += ` (${pathDisplay})`;
      } else if (charType === 'tank') {
        const pathDisplay = upgradeData.path === 'path1' ? 'Path 1' : 'Path 2';
        itemName.textContent += ` (${pathDisplay})`;
      } else if (charType === 'vampire') {
        const pathDisplay = upgradeData.path === 'path1' ? 'Path 1' : 'Path 2';
        itemName.textContent += ` (${pathDisplay})`;
      } else if (charType === 'gunman') {
        const pathDisplay = upgradeData.path === 'path1' ? 'Path 1' : 'Path 2';
        itemName.textContent += ` (${pathDisplay})`;
      } else if (charType === 'knight') {
        const pathDisplay = upgradeData.path === 'path1' ? 'Path 1' : 'Path 2';
        itemName.textContent += ` (${pathDisplay})`;
      } else if (charType === 'ninja') {
        const pathDisplay = upgradeData.path === 'path1' ? 'Clone' : 'Assassin';
        itemName.textContent += ` (${pathDisplay})`;
      } else {
        itemName.textContent += ` (${upgradeData.path.charAt(0).toUpperCase() + upgradeData.path.slice(1)} Path)`;
      }
    }

    // Add character ability description
    const itemDescription = document.createElement('div');
    itemDescription.className = 'upgrade-item-description';

    // Character descriptions in Thai
    const characterDescriptions = {
      'berserk': 'ทุกๆ 3 การโจมตี จะทำให้ การโจมตี ครั้งถัดไป ดูด เลือด 100% ของ ดาเมจ ที่ทำ เเละ เมื่อ hp ต่ำจะเปิด โหมด berserk และได้รับ lifesteal',
      'archer': 'เพิ่มระยะยิง เเละ ความเร็วโจมตี เมื่อ เปิดใช้งานสกิล',
      'tank': 'เมื่อโดน โจมตีจะเพิ่มค่าโล่ เเละ ลดดาเมจที่ได้รับลง',
      'mage': 'เมื่อโจมตี 7 ครั้ง จะยิง Meteor สร้างความเสียหายวงกว้าง',
      'ghost': 'ฟื้นคืนชีพเมื่อตาย และ stun ฝ่ายตรงข้ามทั้งหมด 5 วินาที',
      'vampire': 'สามารถ ดูดเลือด 8% ของศัตรูที่โจมตี ได้',
      'gunman': 'มีโอกาสยิงโดนหัว หากโดน จะ โดนดาเมจที่รุนเเรง (Headshot จะไม่สนพลังป้องกัน)',
      'knight': 'เพิ่มค่า ป้องกัน เเละ พลังโจมตี 5 เท่า',
      'ninja': 'หากเลือดลดลง 50% จะทำการวาปไปด้านหลังตัวที่ใกลที่สุด เเละ ไม่โดนดาเมจช่วงที่ หายตัว',
      'reaper': 'มีโอกาส 20% ที่จะเกิดใหม่เมื่อตาย เเละ หากเกิดใหม่ ได้ จะ เพิ่มโอกาส โกงความตาย หากจัดการศัตรูได้ จะ เพิ่มเลือดตัวเอง',
      'murder': 'มีสกิล ที่หากโจมตี จะทำให้ เป้าหมายเลือดไหล หากจัดการ ศัตรูได้ จะเพิ่ม ดาเมจ ความเร็ว และการป้องกัน',
      'cyborg': 'มีสกิล เปิดโล่ให้ตัวเอง และทุกๆ 5 การโจมตี จะโจมตีหนึ่งครั้งด้วยดาเมจที่รุนแรงมาก และเมื่อตายมีโอกาส 60% ที่จะ recharge หาก recharge ติดจะเพิ่มค่าโล่ให้ตัวเอง 10%',
      'tungtung': 'วาปไปด้านหลังศัตรูและทำให้ศัตรูหลับ 3 วินาที เเละ มีโอกาส 50% ที่จะใช้ sleep อีกครั้งใส่ตัวอื่น (ศัตรูที่โดนสลีปจะเหลือพลังป้องกันแค่ 20% เป็นเวลา 3 วินาที)'
    };

    itemDescription.textContent = characterDescriptions[charType] || 'ไม่มีคำอธิบาย';
    itemDescription.style.cssText = `
      font-size: 0.8rem;
      color: #ccc;
      margin: 5px 0;
      line-height: 1.3;
      font-style: italic;
    `;

    const itemLevel = document.createElement('div');
    itemLevel.className = 'upgrade-item-level';
    itemLevel.textContent = isMaxLevel ? `Level ${currentLevel} (MAX)` : `Level ${currentLevel}`;
    if (isMaxLevel) {
      itemLevel.classList.add('max-level');
    }

    const itemStats = document.createElement('div');
    itemStats.className = 'upgrade-item-stats';
    
    const originalBaseChar = characters.find(c => c.type === charType);
    const currentStats = getCharacterWithUpgrades(charType);
    
    const statMapping = [
      { name: 'HP', key: 'hp' },
      { name: 'ATK', key: 'attack' },
      { name: 'DEF', key: 'defense' },
      { name: 'SPD', key: 'speed' }
    ];

    statMapping.forEach(stat => {
      const statDiv = document.createElement('div');
      statDiv.className = 'upgrade-stat';
      const baseValue = originalBaseChar[stat.key];
      const currentValue = currentStats[stat.key];
      const bonus = Math.round(currentValue - baseValue);
      
      const bonusText = bonus > 0 ? ` (+${bonus})` : '';
      statDiv.textContent = `${stat.name}: ${Math.round(baseValue)}${bonusText}`;
      itemStats.appendChild(statDiv);
    });

    const itemPrice = document.createElement('div');
    itemPrice.className = 'upgrade-item-price';
    const upgradeButtonText = (charType === 'berserk' && currentLevel === 2) ? 'Specialize' : 
                             (charType === 'archer' && currentLevel === 2) ? 'Specialize' : 
                             (charType === 'tank' && currentLevel === 2) ? 'Specialize' : 
                             (charType === 'mage' && currentLevel === 2) ? 'Specialize' : 
                             (charType === 'vampire' && currentLevel === 2) ? 'Specialize' : 
                             (charType === 'gunman' && currentLevel === 2) ? 'Specialize' : 
                             (charType === 'knight' && currentLevel === 2) ? 'Specialize' : 
                             (charType === 'ninja' && currentLevel === 2) ? 'Specialize' : 
    itemPrice.textContent = isMaxLevel ? 'MAX LEVEL' : `${upgradeCost} Coins`;
    if (isMaxLevel) {
      itemPrice.classList.add('max-level');
    }

    const itemButton = document.createElement('button');
    itemButton.className = 'upgrade-item-button';
    itemButton.textContent = isMaxLevel ? 'MAX LEVEL' : upgradeButtonText;
    itemButton.disabled = isMaxLevel || !canAfford;

    if (canAfford) {
      itemButton.classList.add('can-afford');
    }

    if (isMaxLevel) {
      itemButton.classList.add('max-level');
    }

    itemButton.onclick = () => upgradeCharacter(charType);

    upgradeItem.appendChild(itemIcon);
    upgradeItem.appendChild(itemName);
    upgradeItem.appendChild(itemDescription);
    upgradeItem.appendChild(itemLevel);
    upgradeItem.appendChild(itemStats);
    upgradeItem.appendChild(itemPrice);
    upgradeItem.appendChild(itemButton);

    // Add Change Path button for level 3+ characters with paths
    if (currentLevel >= 3 && upgradeData.path && (charType === 'berserk' || charType === 'archer' || charType === 'tank' || charType === 'mage' || charType === 'vampire' || charType === 'gunman' || charType === 'knight' || charType === 'ninja')) {
      const changePathButton = document.createElement('button');
      changePathButton.className = 'upgrade-item-button change-path-button';
      changePathButton.textContent = 'Change Path (500)';
      changePathButton.disabled = gameState.coins < 500;
      
      if (gameState.coins >= 500) {
        changePathButton.classList.add('can-afford');
      }
      
      changePathButton.onclick = () => changeCharacterPath(charType);
      upgradeItem.appendChild(changePathButton);
    }

    upgradeItems.appendChild(upgradeItem);
  });
}

// Update upgrade buttons based on current coin balance
function updateUpgradeButtons() {
  document.getElementById('upgradeCoinBalance').textContent = gameState.coins;

  const upgradeItems = document.querySelectorAll('.upgrade-item');
  upgradeItems.forEach(item => {
    const charType = item.dataset.id;
    const upgradeData = gameState.characterUpgrades[charType] || { level: 0 };
    const currentLevel = upgradeData.level;
    const upgradeCost = getUpgradeCost(charType, currentLevel);
    const isMaxLevel = currentLevel >= 5;
    const button = item.querySelector('.upgrade-item-button');

    if (!button) return;

    const canAfford = gameState.coins >= upgradeCost && !isMaxLevel;
    button.disabled = isMaxLevel || !canAfford;

    if (canAfford) {
      button.classList.add('can-afford');
    } else {
      button.classList.remove('can-afford');
    }

    if (isMaxLevel) {
      button.classList.add('max-level');
    } else {
      button.classList.remove('max-level');
    }

    const upgradeButtonText = (charType === 'berserk' && currentLevel === 2) ? 'Specialize' : 
                             (charType === 'archer' && currentLevel === 2) ? 'Specialize' : 
                             (charType === 'tank' && currentLevel === 2) ? 'Specialize' : 
                             (charType === 'mage' && currentLevel === 2) ? 'Specialize' : 
                             (charType === 'vampire' && currentLevel === 2) ? 'Specialize' : 
                             (charType === 'gunman' && currentLevel === 2) ? 'Specialize' : 
                             (charType === 'knight' && currentLevel === 2) ? 'Specialize' : 
                             (charType === 'ninja' && currentLevel === 2) ? 'Specialize' : 
                             `Upgrade (${upgradeCost} Coins)`;
    button.textContent = isMaxLevel ? 'MAX LEVEL' : upgradeButtonText;

    // Update price display
    const priceElement = item.querySelector('.upgrade-item-price');
    if (priceElement) {
      priceElement.textContent = isMaxLevel ? 'MAX LEVEL' : `${upgradeCost} Coins`;
      if (isMaxLevel) {
        priceElement.classList.add('max-level');
      } else {
        priceElement.classList.remove('max-level');
      }
    }

    // Update level display
    const levelElement = item.querySelector('.upgrade-item-level');
    if (levelElement) {
      levelElement.textContent = isMaxLevel ? `Level ${currentLevel} (MAX)` : `Level ${currentLevel}`;
      if (isMaxLevel) {
        levelElement.classList.add('max-level');
      } else {
        levelElement.classList.remove('max-level');
      }
    }

    // Update Change Path button if it exists
    const changePathButton = item.querySelector('.change-path-button');
    if (changePathButton) {
      const canAffordPathChange = gameState.coins >= 500;
      changePathButton.disabled = !canAffordPathChange;
      
      if (canAffordPathChange) {
        changePathButton.classList.add('can-afford');
      } else {
        changePathButton.classList.remove('can-afford');
      }
    }
  });
}

// Create items shop content - Random Item System
function createItemsShop() {
  const itemsContent = document.getElementById('itemsContent');
  itemsContent.innerHTML = '';

  // Create random item section
  const randomSection = document.createElement('div');
  randomSection.className = 'random-item-section';
  randomSection.innerHTML = `
    <div class="random-item-header">
      <p>ใช้: 150 Coins ต่อครั้ง</p>
      <p>หากได้ไอเท็มซ้ำจะได้เงินคืน 50%</p>
    </div>
    <div class="random-item-content">
      <div class="gacha-box" id="itemGachaBox">
        <div class="gacha-box-inner">
          <div class="gacha-question">?</div>
        </div>
      </div>
      <button class="random-item-button" id="randomItemButton">
        <span class="button-text">Random Item</span>
        <span class="button-cost">150 🪙</span>
      </button>
    </div>
  `;

  itemsContent.appendChild(randomSection);

  // Update button state
  updateRandomItemButton();
}

// Purchase an item (Legacy function - now using random system)
// This function is kept for compatibility but not used in the new random system

// Apply item effects to a character
function applyItemEffects(character) {
  const equippedItems = gameState.equippedItems[character.type];
  if (!equippedItems || equippedItems.length === 0) return character;

  // Create a copy of the character to avoid modifying the original
  const enhancedCharacter = { ...character };

  // Apply effects from all equipped items
  equippedItems.forEach(itemId => {
    const item = items.find(i => i.id === itemId);
    if (!item) return;

    switch (item.effect) {
      case 'attack_boost':
        enhancedCharacter.attack = Math.floor(enhancedCharacter.attack * (1 + item.attackBoost));
        break;
      case 'defense_boost':
        enhancedCharacter.defense = Math.floor(enhancedCharacter.defense * (1 + item.defenseBoost));
        break;
      case 'hp_boost':
        enhancedCharacter.hp = Math.floor(enhancedCharacter.hp * (1 + item.hpBoost));
        enhancedCharacter.maxHealth = enhancedCharacter.hp;
        break;
      case 'crit_boost':
        enhancedCharacter.critChance += item.critBoost;
        break;
      case 'range_boost':
        if (item.restriction === 'range_gt_1' && enhancedCharacter.range > 1) {
          enhancedCharacter.range += item.rangeBoost;
        }
        break;
      case 'lifesteal':
        enhancedCharacter.lifesteal = (enhancedCharacter.lifesteal || 0) + item.lifestealAmount;
        break;
      case 'speed_boost':
        enhancedCharacter.speed = Math.floor(enhancedCharacter.speed * (1 + item.speedBoost));
        break;
      case 'attack_speed_boost':
        enhancedCharacter.attackSpeed = enhancedCharacter.attackSpeed * (1 + item.attackSpeedBoost);
        break;
      case 'death_cheat_boost':
        if (item.restriction === 'reaper_only' && enhancedCharacter.type === 'reaper') {
          enhancedCharacter.deathCheatChance += item.deathCheatBoost;
        }
        break;
      case 'counter_damage':
        enhancedCharacter.counterDamage = item.counterDamage;
        break;
      case 'instant_revive':
        enhancedCharacter.instantReviveCount = (enhancedCharacter.instantReviveCount || 0) + item.reviveCount;
        break;
    }
  });

  // Cap total lifesteal at 20%
  enhancedCharacter.lifesteal = Math.min(enhancedCharacter.lifesteal || 0, 0.20);

  return enhancedCharacter;
}

// Update items buttons based on current coin balance
function updateItemsButtons() {
  document.getElementById('itemsCoinBalance').textContent = gameState.coins;

  // Update random item button
  updateRandomItemButton();
}

// Create update logs content
function createUpdateLogs() {
  const logsContent = document.getElementById('logsContent');
  logsContent.innerHTML = '';

  const updateLogs = [
    {
      date:'08-07-2024',
      changes: [
        {text: "new character tungtung",type:'new'},
        {text: "buff tank base HP", type:"improved"}
      ]
    },
    {
      date: '07-07-2024',
      changes: [
        {text: "make item can equip 1 to 3", type:"improved"},
        {text: "nerf Cyborg Charge 80% to 60%", type:"improved"},
        {text: 'Infinite mode: character selection is locked after start, unlocked on play again or re-enter', type: 'improved'},
        {text: 'Nerf Murder blooding chance 40% → 10%', type: 'improved'},
        {text: 'Nerf Gunman damage 9 → 7 and attack cooldown to 1.2', type: 'improved'},
        {text: 'Nerf Archer All status', type: 'improved'},
        {text: 'Buff Tank', type: 'improved'}
      ]
    },
    {
      date: '05-07-2024',
      changes: [
        { text: 'Add new Character cyborg', type: 'new'},
        { text: 'Add new system is item', type: 'new'},
        { text: 'Add new character is cyborg', type: 'new'}
      ]
    },
    {
      date: '04-07-2024',
      changes: [
        { text: 'Vampire defense 5 => 2 and buff the passive vampire', type: 'improved' },
        { text: 'Buff GunMan Head Shot Chance 10% to 40%', type: 'new' },
        { text: 'Buff Murder Damage', type: 'new'},
        { text: 'Tank Path1 Buff', type: 'new'},
        { text: 'Buff Murder Movement speed', type: 'new'},
        { text: 'Buff Ghost HP', type: 'new'}, 
        { text: 'Gunman if Headshot now ignores defense enemy', type:'new'},
        { text: 'Nerf Vampire', type:'improved'},
        { text: 'Buff Vampire Path1', type:'new'}
      ]
    },
    {
      date: '03-07-2024',
      changes: [
        { text: 'Added new Infinite Mode for endless battles', type: 'new' },
        { text: 'Optimized performance for mobile devices', type: 'improved' }
      ]
    },
    {
      date: '26-06-2024',
      changes: [
        { text: 'Complete UI redesign', type: 'new' },
        { text: 'Improve Upgrade System', type: 'improved'}
      ]
    },
    {
      date: '22-06-2024',
      changes: [
        { text: 'Improved AI behavior and targeting system', type: 'improved' },
        { text: 'Enhanced visual feedback for attacks', type: 'improved' },
        { text: 'Enhanced Mage abilities with enhanced and ice meteor spells', type: 'improved' },
        { text: 'Added new character: Ninja', type: 'new' },
        { text: 'Added character upgrade system with 5 levels', type: 'new' }
      ]
    },
    {
      date: '17-06-2024',
      changes: [
        { text: 'สร้างเกมไงไอควาย', type: 'new' }
      ]
    }
  ];

  updateLogs.forEach(log => {
    const logEntry = document.createElement('div');
    logEntry.className = 'log-entry';

    const logDate = document.createElement('div');
    logDate.className = 'log-date';
    logDate.textContent = log.date;

    const logChanges = document.createElement('ul');
    logChanges.className = 'log-changes';

    log.changes.forEach(change => {
      const changeItem = document.createElement('li');
      changeItem.textContent = change.text;
      changeItem.classList.add(change.type);
      logChanges.appendChild(changeItem);
    });

    logEntry.appendChild(logDate);
    logEntry.appendChild(logChanges);
    logsContent.appendChild(logEntry);
  });
}

// Upgrade screen
document.getElementById('closeUpgradeButton').addEventListener('click', () => {
  gameState.currentScreen = 'home';
  saveGameState();
  showScreen('home');
});

function setBerserkPath(path) {
  const upgradeCost = getUpgradeCost('berserk', 2);
  if (gameState.coins < upgradeCost) {
    showNotification('Not enough coins for upgrade!', 'error');
    return;
  }

  gameState.coins -= upgradeCost;
  gameState.characterUpgrades['berserk'] = {
    level: 3,
    path: path
  };

  saveGameState();
  updateUI();

  if (gameState.currentScreen === 'upgrade') {
    createUpgradeItems();
  }

  showNotification(`Berserk has specialized in the ${path} path!`, 'success');
}

function setArcherPath(path) {
  const upgradeCost = getUpgradeCost('archer', 2);
  if (gameState.coins < upgradeCost) {
    showNotification('Not enough coins for upgrade!', 'error');
    return;
  }

  gameState.coins -= upgradeCost;
  gameState.characterUpgrades['archer'] = {
    level: 3,
    path: path
  };

  saveGameState();
  updateUI();

  if (gameState.currentScreen === 'upgrade') {
    createUpgradeItems();
  }

  showNotification(`Archer has specialized in the ${path} path!`, 'success');
}

function setTankPath(path) {
  const upgradeCost = getUpgradeCost('tank', 2);
  if (gameState.coins < upgradeCost) {
    showNotification('Not enough coins for upgrade!', 'error');
    return;
  }

  gameState.coins -= upgradeCost;
  gameState.characterUpgrades['tank'] = {
    level: 3,
    path: path
  };

  saveGameState();
  updateUI();

  if (gameState.currentScreen === 'upgrade') {
    createUpgradeItems();
  }

  showNotification(`Tank has specialized in the ${path} path!`, 'success');
}

function setMagePath(path) {
  const upgradeCost = getUpgradeCost('mage', 2);
  if (gameState.coins < upgradeCost) {
    showNotification('Not enough coins for upgrade!', 'error');
    return;
  }

  gameState.coins -= upgradeCost;
  gameState.characterUpgrades['mage'] = {
    level: 3,
    path: path
  };

  saveGameState();
  updateUI();

  if (gameState.currentScreen === 'upgrade') {
    createUpgradeItems();
  }

  showNotification(`Mage has specialized in the ${path} path!`, 'success');
}

function setVampirePath(path) {
  const upgradeCost = getUpgradeCost('vampire', 2);
  if (gameState.coins < upgradeCost) {
    showNotification('Not enough coins for upgrade!', 'error');
    return;
  }
  gameState.coins -= upgradeCost;
  gameState.characterUpgrades['vampire'] = {
    level: 3,
    path: path
  };
  saveGameState();
  updateUI();
  if (gameState.currentScreen === 'upgrade') {
    createUpgradeItems();
  }
  showNotification(`Vampire has specialized in the ${path} path!`, 'success');
}

function setGunmanPath(path) {
  const upgradeCost = getUpgradeCost('gunman', 2);
  if (gameState.coins < upgradeCost) {
    showNotification('Not enough coins for upgrade!', 'error');
    return;
  }
  gameState.coins -= upgradeCost;
  gameState.characterUpgrades['gunman'] = {
    level: 3,
    path: path
  };
  saveGameState();
  updateUI();
  if (gameState.currentScreen === 'upgrade') {
    createUpgradeItems();
  }
  showNotification(`Gunman has specialized in the ${path} path!`, 'success');
}

function setKnightPath(path) {
  const upgradeCost = getUpgradeCost('knight', 2);
  if (gameState.coins < upgradeCost) {
    showNotification('Not enough coins for upgrade!', 'error');
    return;
  }
  gameState.coins -= upgradeCost;
  gameState.characterUpgrades['knight'] = {
    level: 3,
    path: path
  };
  saveGameState();
  updateUI();
  if (gameState.currentScreen === 'upgrade') {
    createUpgradeItems();
  }
  showNotification(`Knight has specialized in the ${path} path!`, 'success');
}

function setNinjaPath(path) {
  const upgradeCost = getUpgradeCost('ninja', 2);
  if (gameState.coins < upgradeCost) {
    showNotification('Not enough coins for upgrade!', 'error');
    return;
  }
  gameState.coins -= upgradeCost;
  gameState.characterUpgrades['ninja'] = {
    level: 3,
    path: path
  };
  saveGameState();
  updateUI();
  if (gameState.currentScreen === 'upgrade') {
    createUpgradeItems();
  }
  showNotification(`Ninja has specialized in the ${path} path!`, 'success');
}

// Function to change character path for 500 coins
function changeCharacterPath(characterType) {
  const upgradeData = gameState.characterUpgrades[characterType];
  if (!upgradeData || upgradeData.level < 3) {
    showNotification('Character must be level 3 or higher to change path!', 'error');
    return;
  }

  const pathChangeCost = 500;

  if (gameState.coins < pathChangeCost) {
    showNotification('Not enough coins! Path change costs 500 coins.', 'error');
    return;
  }

  // Determine current path
  const currentPath = upgradeData.path;
  let message = '';
  let path1 = 'path1';
  let path2 = 'path2';
  let path1Detail = '';
  let path2Detail = '';
  let buttons = [];
  switch (characterType) {
    case 'berserk':
      path1Detail = 'path1: เพิ่มพลังป้องกันขึ้นหลายเท่าเมื่อเข้าสู่โหมด Berserk และเพิ่ม HP ถาวร 10% กับ DEF 15% + Lifesteal 15%\n\npath2: เป็นอมตะ 2 วินาทีเมื่อเข้าสู่โหมด Berserk และเพิ่ม ATK ถาวร 12% + Lifesteal 20%';
      path2Detail = '';
      break;
    case 'archer':
      path1Detail = 'path1: เพิ่มระยะยิงและยิงธูปสองดอกพร้อมกันเมื่อใช้สกิล\n\npath2: path2: เพิ่มดาเมจพื้นฐาน เเละ เมื่อกำจัดศัตรูได้จะยิงธูป 5 ดอกใส่ศัตรูที่ใกล้ที่สุดโดยอัตโนมัติ';
      path2Detail = '';
      break;
    case 'tank':
      path1Detail = 'path1: ฟื้นฟูตัวเอง สุ่มๆ 1-50 ของ hp ตัวเอง เมื่อโดนโจมตี (คูลดาวน์ 7 วิ)\n\npath2: เปิดโล่ให้ทุกคนในทีม เมื่อตนเอง โดนโจมตี';
      path2Detail = '';
      break;
    case 'mage':
      path1Detail = 'path1: Meteor รุนแรงขึ้นและคูลดาวน์ไวขึ้น\n\npath2: Meteor จะเเช่เเข็ง ศัตรู 3 วินาที';
      path2Detail = '';
      break;
    case 'vampire':
      path1Detail = 'path1: จะทำการ ลดเลือด ตัวเอง 60% เเละ โจมตีศัตรูที่ใกล้ที่สุด ตามเลือดที่ลดลง\n\npath2: ดูดเลือดแรงมาก และเมื่อกำจัดศัตรูได้จะระเบิดศัตรูเป็นวงกว้าง ดูดเลือดจากศัตรูรอบข้าง 20% ของ HP';
      path2Detail = '';
      break;
    case 'gunman':
      path1Detail = '';
      path2Detail = 'path2: ยิงไกล เเละ ดาเมจสูง';
      break;
    case 'knight':
      path1Detail = 'path1: ฟื้นฟูตัวเอง 80% ของดาเมจ ที่ได้รับ เเละ ทุกครั้งที่โจมตี จะเพิ่ม ค่า damage เเละ ความเร็วเคลื่อนที่ 0.1 หน่วย\n\npath2: หากตายจะมีโอกาส 40% ที่จะเกิดใหม่';
      path2Detail = '';
      break;
    case 'ninja':
      path1Detail = 'Path 1: เเยกร่างออกมา 2 ตัว อยู่ได้ได้นาน 7 วิ หรือ จนตาย\n\nPath 2: เมื่อเริ่มการต่อสู้ จะวาร์ปไปด้านหลังศัตรูตัวระยะไกล (ประเภทโจมตีไกล) และสร้างความเสียหาย ที่รุนเเรง แล้ววาร์ปกลับ';
      path2Detail = '';
      break;
    default:
      path1Detail = 'path1';
      path2Detail = 'path2';
  }
  message = path1Detail && path2Detail ? `${path1Detail}\n\n${path2Detail}` : path1Detail || path2Detail;
  // Only show the button for the path that is NOT the current one
  if (currentPath === 'path1') {
    buttons = [
      { text: 'Path 2', action: () => confirmPathChange(characterType, 'path2', pathChangeCost) },
      { text: 'Cancel', secondary: true }
    ];
  } else if (currentPath === 'path2') {
    buttons = [
      { text: 'Path 1', action: () => confirmPathChange(characterType, 'path1', pathChangeCost) },
      { text: 'Cancel', secondary: true }
    ];
  } else {
    // If for some reason no path is set, show both
    buttons = [
      { text: 'Path 1', action: () => confirmPathChange(characterType, 'path1', pathChangeCost) },
      { text: 'Path 2', action: () => confirmPathChange(characterType, 'path2', pathChangeCost) },
      { text: 'Cancel', secondary: true }
    ];
  }

  showModal(
    'Change Path',
    message,
    buttons
  );
}

function confirmPathChange(characterType, newPath, cost) {
  gameState.coins -= cost;
  gameState.characterUpgrades[characterType].path = newPath;
  
  saveGameState();
  updateUI();
  
  if (gameState.currentScreen === 'upgrade') {
    createUpgradeItems();
  }
  
  showNotification(`${characterType} path changed to ${newPath}!`, 'success');
}

function castEnhancedMeteor(mage, enemies) {
  const battlefield = document.getElementById('battlefield');
  const target = findTarget(mage, enemies);
  if (!target) return;
  const meteorX = target.x + 20;
  const meteorY = target.y + 20;
  const meteor = document.createElement('div');
  meteor.className = 'projectile meteor enhanced';
  meteor.textContent = '🔥';
  meteor.style.fontSize = '24px';
  const startX = meteorX;
  const startY = -100;
  meteor.style.left = `${startX}px`;
  meteor.style.top = `${startY}px`;
  battlefield.appendChild(meteor);

  setTimeout(() => {
    meteor.style.transform = `translate(0, ${meteorY - startY}px)`;
  }, 10);

  setTimeout(() => {
    meteor.remove();
    const explosion = document.createElement('div');
    explosion.className = 'explosion-effect enhanced';
    explosion.style.left = `${target.x - 40}px`;
    explosion.style.top = `${target.y - 40}px`;
    explosion.style.width = '120px';
    explosion.style.height = '120px';
    battlefield.appendChild(explosion);
    setTimeout(() => explosion.remove(), 1000);

    // Enhanced meteor damage and larger radius
    const aoeRadius = 150; // Increased from 100
    const meteorDamage = Math.floor(mage.attack * 4); // Increased from 3

    enemies.forEach(enemy => {
      const dist = getDistance(target.x, target.y, enemy.x, enemy.y);
      if (dist <= aoeRadius && enemy.health > 0) {
        enemy.health = Math.max(0, enemy.health - meteorDamage);
        const enemyEl = document.getElementById(enemy.id);
        if (enemyEl) {
          const healthBar = enemyEl.querySelector('.health-bar-fill');
          healthBar.style.width = `${(enemy.health / enemy.maxHealth) * 100}%`;
        }
        showDamageText(enemy.x, enemy.y, meteorDamage, 'crit');
        if (enemy.health <= 0) {
          handleUnitDeath(mage, enemy);
        }
      }
    });

    const boomText = document.createElement('div');
    boomText.className = 'damage-text crit';
    boomText.textContent = 'MEGA BOOM!';
    boomText.style.left = `${target.x}px`;
    boomText.style.top = `${target.y}px`;
    battlefield.appendChild(boomText);
    setTimeout(() => boomText.remove(), 1000);
  }, 710);
}

function castIceMeteor(mage, enemies) {
  const battlefield = document.getElementById('battlefield');
  const target = findTarget(mage, enemies);
  if (!target) return;
  const meteorX = target.x + 20;
  const meteorY = target.y + 20;
  const meteor = document.createElement('div');
  meteor.className = 'projectile meteor ice';
  meteor.textContent = '❄️';
  meteor.style.fontSize = '24px';
  meteor.style.color = '#00ffff'; // Blue color
  const startX = meteorX;
  const startY = -100;
  meteor.style.left = `${startX}px`;
  meteor.style.top = `${startY}px`;
  battlefield.appendChild(meteor);

  setTimeout(() => {
    meteor.style.transform = `translate(0, ${meteorY - startY}px)`;
  }, 10);

  setTimeout(() => {
    meteor.remove();
    const explosion = document.createElement('div');
    explosion.className = 'explosion-effect ice';
    explosion.style.left = `${target.x - 30}px`;
    explosion.style.top = `${target.y - 30}px`;
    explosion.style.width = '100px';
    explosion.style.height = '100px';
    explosion.style.background = 'radial-gradient(circle, rgba(0,255,255,0.8) 0%, rgba(0,150,255,0.6) 50%, rgba(0,100,255,0.3) 100%)';
    explosion.style.borderRadius = '50%';
    explosion.style.zIndex = '100';
    battlefield.appendChild(explosion);
    setTimeout(() => explosion.remove(), 800);

    // Ice meteor damage and freeze effect
    const aoeRadius = 100;
    const meteorDamage = Math.floor(mage.attack * 3);

    enemies.forEach(enemy => {
      const dist = getDistance(target.x, target.y, enemy.x, enemy.y);
      if (dist <= aoeRadius && enemy.health > 0) {
        // Apply damage
        enemy.health = Math.max(0, enemy.health - meteorDamage);
        const enemyEl = document.getElementById(enemy.id);
        if (enemyEl) {
          const healthBar = enemyEl.querySelector('.health-bar-fill');
          healthBar.style.width = `${(enemy.health / enemy.maxHealth) * 100}%`;
          
          // Freeze effect - make enemy blue and immobile
          enemyEl.style.filter = 'brightness(1.2) saturate(1.5) hue-rotate(180deg)';
          enemy.frozen = true;
          enemy.freezeStartTime = Date.now();
          
          // Remove freeze effect after 3 seconds
          setTimeout(() => {
            if (enemy.frozen) {
              enemy.frozen = false;
              enemyEl.style.filter = '';
            }
          }, 3000);
        }
        showDamageText(enemy.x, enemy.y, meteorDamage, 'crit');
        if (enemy.health <= 0) {
          handleUnitDeath(mage, enemy);
        }
      }
    });

    const boomText = document.createElement('div');
    boomText.className = 'damage-text crit';
    boomText.textContent = 'ICE BLAST!';
    boomText.style.left = `${target.x}px`;
    boomText.style.top = `${target.y}px`;
    boomText.style.color = '#00ffff';
    battlefield.appendChild(boomText);
    setTimeout(() => boomText.remove(), 1000);
  }, 710);
}

// --- Vampire Immortal (ขั้นสาม path1) ---
function handleVampireImmortal(unit, duration = 1000) {
  unit.isImmortal = true;
  unit.lifestealImmortalBoost = true;
  const unitEl = document.getElementById(unit.id);
  if (unitEl) unitEl.style.filter = 'drop-shadow(0 0 20px #fff) brightness(1.7)';
  setTimeout(() => {
    unit.isImmortal = false;
    unit.lifestealImmortalBoost = false;
    if (unitEl) unitEl.style.filter = '';
  }, duration);
}
// --- Vampire AoE (ขั้นสาม path2) ---
function handleVampireAoE(attacker, target) {
  const aoeRadius = 100;
  const battlefield = document.getElementById('battlefield');
  const allUnits = attacker.player === 1 ? player2Units : player1Units;
  const attackerEl = document.getElementById(attacker.id);
  allUnits.forEach(enemy => {
    if (enemy && enemy.health > 0 && enemy.id !== target.id) {
      const dist = getDistance(target.x, target.y, enemy.x, enemy.y);
      if (dist <= aoeRadius) {
        const aoeDmg = Math.floor(enemy.maxHealth * 0.15); // 15% max HP
        enemy.health = Math.max(0, enemy.health - aoeDmg);
        const enemyEl = document.getElementById(enemy.id);
        if (enemyEl) {
          const healthBar = enemyEl.querySelector('.health-bar-fill');
          healthBar.style.width = `${(enemy.health / enemy.maxHealth) * 100}%`;
          createHitEffect(enemy.id);
        }
        showDamageText(enemy.x, enemy.y, aoeDmg, 'crit');
        // Heal vampire for 25% of that enemy's HP
        const heal = Math.floor(enemy.maxHealth * 0.25);
        attacker.health = Math.min(attacker.maxHealth, attacker.health + heal);
        if (attackerEl) {
          const healthBar = attackerEl.querySelector('.health-bar-fill');
          healthBar.style.width = `${(attacker.health / attacker.maxHealth) * 100}%`;
        }
        showDamageText(attacker.x, attacker.y, heal, 'heal');
        if (enemy.health <= 0) handleUnitDeath(attacker, enemy);
      }
    }
  });
  // Visual explosion effect
  if (battlefield) {
    const explosion = document.createElement('div');
    explosion.className = 'explosion-effect';
    explosion.style.left = `${target.x - 30}px`;
    explosion.style.top = `${target.y - 30}px`;
    battlefield.appendChild(explosion);
    setTimeout(() => explosion.remove(), 800);
  }
}

function handleAssassinSkill(unit) {
  const enemies = unit.player === 1 ? player2Units.filter(u => u && u.health > 0) : player1Units.filter(u => u && u.health > 0);
  const rangedEnemies = enemies.filter(e => e.range > 1);
  
  // If there are no ranged enemies, target the furthest enemy of any type
  const target = rangedEnemies.length > 0 ? findBackEnemy(rangedEnemies, unit.player) : findBackEnemy(enemies, unit.player);

  if (target) {
    unit.isImmune = true; // Become immune for the duration of the skill

    const unitEl = document.getElementById(unit.id);
    const originalX = unit.x;
    const originalY = unit.y;

    // Teleport behind target
    const targetX = target.x + (unit.player === 1 ? -40 : 40);
    const targetY = target.y;

    if (unitEl) {
      // Vanish effect
      unitEl.style.opacity = '0';
      setTimeout(() => {
        unit.x = targetX;
        unit.y = targetY;
        unitEl.style.left = `${unit.x}px`;
        unitEl.style.top = `${unit.y}px`;
        unitEl.style.opacity = '1';
        
        // Attack with high damage
        const damage = unit.attack * 6; // Powerful single strike, buffed from 4x to 6x
        shootProjectile(unit, target, 'slash', damage, true);
        setTimeout(() => {
          applyDamage(unit, target, damage, true);
          // Stun the target for 2 seconds after dealing damage
          if (target.health > 0) {
              target.stunned = true;
              const targetEl = document.getElementById(target.id);
              if (targetEl) {
                  const stunEffect = document.createElement('div');
                  stunEffect.className = 'stun-effect';
                  stunEffect.textContent = '💫';
                  targetEl.appendChild(stunEffect);
                  
                  setTimeout(() => {
                      stunEffect.remove();
                      target.stunned = false;
                  }, 2000);
              } else {
                  setTimeout(() => {
                      target.stunned = false;
                  }, 2000);
              }
          }
        }, 410);

        // Teleport back
        setTimeout(() => {
          unitEl.style.opacity = '0';
          setTimeout(() => {
            unit.x = originalX;
            unit.y = originalY;
            unitEl.style.left = `${unit.x}px`;
            unitEl.style.top = `${unit.y}px`;
            unitEl.style.opacity = '1';
            unit.isImmune = false; // Immunity wears off after returning
          }, 200);
        }, 500);
      }, 200);
    } else {
        // Fallback to remove immunity if the element is not found
        setTimeout(() => {
            if(unit) unit.isImmune = false;
        }, 900);
    }
  }
}

function createClones(originalUnit, count) {
    const battlefield = document.getElementById('battlefield');
    const clonesArray = originalUnit.player === 1 ? player1Clones : player2Clones;

    for (let i = 0; i < count; i++) {
        const clone = { ...originalUnit }; // Copy original unit properties

        // Set clone-specific properties
        clone.id = `p${originalUnit.player}-clone-${Date.now()}-${i}`;
        clone.uniqueId = nextUnitUniqueId++; // เพิ่ม uniqueId ให้ clone
        clone.isClone = true;
        clone.isTaunting = true;
        clone.disappearTime = Date.now() + 7000; // Lasts 7 seconds, buffed from 5

        // Set HP to current HP of summoner, inheriting maxHealth
        clone.maxHealth = originalUnit.maxHealth;
        clone.health = originalUnit.health;

        // Reduce stats by 25% (i.e. 75% of original), buffed from 50%
        clone.attack = Math.floor(originalUnit.attack * 0.75);
        clone.defense = Math.floor(originalUnit.defense * 0.75);
        clone.speed = Math.floor(originalUnit.speed * 0.75);
        clone.critChance = Math.floor(originalUnit.critChance * 0.75);
        clone.accuracy = Math.floor(originalUnit.accuracy * 0.75);
        clone.attackSpeed = originalUnit.attackSpeed * 0.75;
        clone.attackCooldown = 1000 / clone.attackSpeed;
        
        // Set initial position at the caster's location
        clone.x = originalUnit.x;
        clone.y = originalUnit.y;

        // Set target position in front of the caster so they rush forward
        const forwardDistance = 80;
        const spreadDistance = 40;
        const direction = originalUnit.player === 1 ? 1 : -1;
        clone.targetX = originalUnit.x + (direction * forwardDistance);
        clone.targetY = originalUnit.y + (i === 0 ? -spreadDistance : spreadDistance);

        // Give clones a temporary speed boost
        const originalCloneSpeed = clone.speed;
        clone.speed *= 2; // Double speed to get in position
        setTimeout(() => {
            if(clone) clone.speed = originalCloneSpeed;
        }, 1000); // Speed boost lasts for 1 second
        
        clonesArray.push(clone);

        // Create DOM element for the clone
        const healthPercentage = (clone.health / clone.maxHealth) * 100;
        const unitElement = document.createElement('div');
        unitElement.className = `unit player${originalUnit.player} clone`; // Add 'clone' class for styling
        unitElement.innerHTML = `
            ${clone.name}
            <div class="health-bar">
                <div class="health-bar-fill" style="width: ${healthPercentage}%"></div>
            </div>
        `;
        unitElement.id = clone.id;
        unitElement.style.left = `${clone.x}px`;
        unitElement.style.top = `${clone.y}px`;
        unitElement.style.boxShadow = '0 0 10px 3px rgba(255, 69, 69, 0.7)'; // Red aura for taunt

        battlefield.appendChild(unitElement);
    }
}

// ฟังก์ชันลบทุกอย่างออกจาก battlefield
function clearBattlefieldEffects() {
  const battlefield = document.getElementById('battlefield');
  // ลบ unit
  battlefield.querySelectorAll('.unit').forEach(el => el.remove());
  // ลบ projectile
  battlefield.querySelectorAll('.projectile').forEach(el => el.remove());
  // ลบ effect พิเศษ
  battlefield.querySelectorAll('.explosion-effect, .explosion-effect.enhanced, .blood-explosion-effect, .blood-splatter, .drain-effect, .stun-effect, .damage-text, .ghost-form, .clone').forEach(el => el.remove());
  // ลบ health bar ที่อาจหลงเลือน
  battlefield.querySelectorAll('.health-bar, .health-bar-fill').forEach(el => el.remove());
}

// --- PAUSE SYSTEM FOR TAB VISIBILITY ---
let gamePausedByTab = false;
let pauseModalOverlay = null;

function pauseGameByTab() {
  if (battleStarted && !gameEnded && !gamePausedByTab) {
    battleStarted = false;
    gamePausedByTab = true;
    
    pauseModalOverlay = showModal(
      'Game Paused',
      'The game is paused because you switched tabs or minimized the browser.',
      [
        {
          text: 'Continue',
          action: () => {
            gamePausedByTab = false;
            battleStarted = true;
            pauseModalOverlay = null;
            
            requestAnimationFrame(gameLoop);
          }
        },
        {
          text: 'Go to Menu',
          secondary: true,
          action: () => {
            gamePausedByTab = false;
            pauseModalOverlay = null;
            showScreen('home');
            updateUI();
          }
        }
      ],
      { hideClose: true }
    );
  }
}

function pauseGameByRotate() {
  if (battleStarted && !gameEnded && !gamePausedByTab) {
    battleStarted = false;
    gamePausedByTab = true;
    
    pauseModalOverlay = showModal(
      'Game Paused',
      'The game is paused.',
      [
        {
          text: 'Continue',
          action: () => {
            gamePausedByTab = false;
            battleStarted = true;
            pauseModalOverlay = null;
            
            requestAnimationFrame(gameLoop);
          }
        },
        {
          text: 'Go to Menu',
          secondary: true,
          action: () => {
            gamePausedByTab = false;
            pauseModalOverlay = null;
            showScreen('home');
            updateUI();
          }
        }
      ],
      { hideClose: true }
    );
  }
}

// Listen for tab visibility changes
if (typeof document.hidden !== 'undefined') {
  document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
      pauseGameByRotate();
    }
  });
}

document.addEventListener('DOMContentLoaded', function() {
  const fullscreenButton = document.getElementById('fullscreenButton');

  fullscreenButton.addEventListener('click', toggleFullScreen);

  function toggleFullScreen() {
    if (!document.fullscreenElement && !document.webkitFullscreenElement) {
      if (document.documentElement.requestFullscreen) {
        document.documentElement.requestFullscreen();
      } else if (document.documentElement.webkitRequestFullscreen) {
        document.documentElement.webkitRequestFullscreen();
      } else if (document.documentElement.msRequestFullscreen) {
        document.documentElement.msRequestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen();
      }
    }
  }

  document.addEventListener('fullscreenchange', updateFullscreenButtonText);
  document.addEventListener('webkitfullscreenchange', updateFullscreenButtonText);
  document.addEventListener('mozfullscreenchange', updateFullscreenButtonText);
  document.addEventListener('MSFullscreenChange', updateFullscreenButtonText);

  function updateFullscreenButtonText() {
    if (document.fullscreenElement || document.webkitFullscreenElement) {
      fullscreenButton.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3"></path>
        </svg>
        Exit FullScreen
      `;
    } else {
      fullscreenButton.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"></path>
        </svg>
        FullScreen
      `;
    }
  }

  function checkOrientation() {
    const orientationMessage = document.querySelector('.orientation-message');
    const gameContent = document.querySelector('.game-content');
    if (window.innerWidth < window.innerHeight) {
      pauseGameByRotate();
      orientationMessage.style.display = 'flex';
      gameContent.style.display = 'none';
    } else {
      orientationMessage.style.display = 'none';
      gameContent.style.display = 'block';
    }
  }

  window.addEventListener('load', checkOrientation);
  window.addEventListener('resize', checkOrientation);
});

// Infinite Mode logic
function startInfiniteMode() {
  gameState.currentMode = 'infinite';
  gameState.infiniteWave = 1;
  showScreen('game');
  document.getElementById('gameModeInfo').textContent = 'Infinite Mode - Wave 1';
  resetGame();
  // Set up the first wave of enemies
  setupInfiniteEnemies(gameState.infiniteWave);
  
  // Track infinite mode start
  trackGameAction('Infinite Mode Started');

  // --- Add Surrender Button ---
  let surrenderBtn = document.getElementById('surrenderButton');
  if (!surrenderBtn) {
    surrenderBtn = document.createElement('button');
    surrenderBtn.id = 'surrenderButton';
    surrenderBtn.className = 'btn';
    surrenderBtn.textContent = 'Surrender';
    surrenderBtn.style.marginLeft = '12px';
    surrenderBtn.style.backgroundColor = '#ff4655';
    surrenderBtn.style.color = '#fff';
    surrenderBtn.style.fontWeight = 'bold';
    surrenderBtn.style.border = 'none';
    surrenderBtn.style.borderRadius = '4px';
    surrenderBtn.style.padding = '6px 16px';
    surrenderBtn.style.boxShadow = '0 2px 8px rgba(0,0,0,0.08)';
    surrenderBtn.style.transition = 'background 0.2s';
    surrenderBtn.onmouseenter = () => surrenderBtn.style.backgroundColor = '#d32f2f';
    surrenderBtn.onmouseleave = () => surrenderBtn.style.backgroundColor = '#ff4655';
    // Insert next to gameModeInfo
    const gameModeInfo = document.getElementById('gameModeInfo');
    if (gameModeInfo && gameModeInfo.parentNode) {
      gameModeInfo.parentNode.insertBefore(surrenderBtn, gameModeInfo.nextSibling);
    }
  } else {
    surrenderBtn.style.display = 'inline-block';
  }
  surrenderBtn.onclick = function() {
    if (gameState.currentMode === 'infinite' && battleStarted && !gameEnded) {
      battleStarted = false;
      gameEnded = true;
      handleInfiniteResult(false);
      clearBattlefieldEffects();
    }
  };
}

function setupInfiniteEnemies(wave) {
  player2Units = [];
  player2Slots = [0, 1, 2, 3]; // allow up to 4 enemies

  // Randomly decide number of enemies: 3 or 4 (20% chance for 4)
  const numEnemies = Math.random() < 0.2 ? 4 : 3;

  // Every 5th wave is a boss wave
  const isBoss = wave % 5 === 0;

  // Calculate enemy power scaling (make early waves easier)
  let enemyPower;
  if (wave <= 3) {
    enemyPower = 0.7 + wave * 0.15; // wave 1: 0.85, wave 2: 1.0, wave 3: 1.15
  } else {
    enemyPower = 1.10 * Math.pow(1.03, wave - 3); // more gradual ramp
  }
  if (isBoss) enemyPower *= 1.25;

  // Randomly pick enemy types
  for (let i = 0; i < numEnemies; i++) {
    let pool = characters.filter(c => c.type !== 'Dummy');
    if (isBoss && i === 0) {
      const bossChar = pool[Math.floor(Math.random() * pool.length)];
      const bossStats = { ...bossChar };
      bossStats.hp = Math.floor(bossStats.hp * enemyPower * 1.5);
      bossStats.attack = Math.floor(bossStats.attack * enemyPower * 1.2);
      bossStats.defense = Math.floor(bossStats.defense * enemyPower * 1.2);
      bossStats.speed = Math.floor(bossStats.speed * 0.9);
      bossStats.isBoss = true;
      selectCharacter(bossStats, 2, bossStats);
      continue;
    }
    const char = pool[Math.floor(Math.random() * pool.length)];
    const stats = { ...char };
    stats.hp = Math.floor(stats.hp * enemyPower);
    stats.attack = Math.floor(stats.attack * enemyPower);
    stats.defense = Math.floor(stats.defense * enemyPower);
    selectCharacter(stats, 2, stats);
  }
}

// Patch gameLoop for infinite mode progression
const originalInfiniteGameLoop = gameLoop;
gameLoop = function() {
  if (!battleStarted) return;
  const now = Date.now();
  const elapsed = now - lastUpdate;

  if (elapsed > frameTime) {
    updateAI();

    const allUnits = [...player1Units, ...player2Units, ...player1Clones, ...player2Clones].filter(unit => unit && (unit.health > 0 || unit.isRecharging));
    // Clone cleanup
    const now = Date.now();
    player1Clones = player1Clones.filter(clone => {
        if (clone.health <= 0 || now >= clone.disappearTime) {
            const el = document.getElementById(clone.id);
            if (el) el.remove();
            return false;
        }
        return true;
    });
    player2Clones = player2Clones.filter(clone => {
        if (clone.health <= 0 || now >= clone.disappearTime) {
            const el = document.getElementById(clone.id);
            if (el) el.remove();
            return false;
        }
        return true;
    });

    for (const unit of allUnits) {
      updateUnitPosition(unit);
      if (unit.bleeding && unit.bleeding.active) {
        const bleedElapsed = now - unit.bleeding.lastTick;
        const damage = unit.bleeding.damagePerSecond * (bleedElapsed / 1000);
        unit.health -= damage;
        unit.bleeding.duration -= bleedElapsed;
        unit.bleeding.lastTick = now;
        if (unit.bleeding.duration <= 0) {
          unit.bleeding.active = false;
          const unitEl = document.getElementById(unit.id);
          if (unitEl) {
            const healthBar = unitEl.querySelector('.health-bar-fill');
            healthBar.classList.remove('health-bar-bleeding');
          }
        }
        const unitEl = document.getElementById(unit.id);
        if (unitEl) {
          const healthBar = unitEl.querySelector('.health-bar-fill');
          healthBar.style.width = `${(unit.health / unit.maxHealth) * 100}%`;
        }
        if (unit.health <= 0) {
          handleUnitDeath(null, unit);
        }
      }
    }

    const player1Alive = player1Units.some(u => u && (u.health > 0 || u.isRecharging));
    const player2Alive = player2Units.some(u => u && (u.health > 0 || u.isRecharging));

    // Infinite mode progression
    if (gameState.currentMode === 'infinite') {
      if (!player1Alive) {
        battleStarted = false;
        gameEnded = true;
        handleInfiniteResult(false);
        clearBattlefieldEffects();
        return;
      } else if (!player2Alive) {
        // Player wins wave, go to next wave immediately
        battleStarted = false;
        gameEnded = false;
        gameState.infiniteWave = (gameState.infiniteWave || 1) + 1;
        // Clean up only enemy units and effects, keep player1Units as is
        clearEnemyUnitsAndEffects();
        // Reset only enemy units and slots
        player2Units = [];
        player2Slots = [0, 1, 2, 3];
        player2Clones = [];
        // Update wave display
        document.getElementById('gameModeInfo').textContent = `Infinite Mode - Wave ${gameState.infiniteWave}`;
        // Set up next wave of enemies
        setupInfiniteEnemies(gameState.infiniteWave);
        // Wait a short moment, then auto-start the next battle
        setTimeout(() => {
          // Restore ghost form visual for player1 units that are still ghosts
          player1Units.forEach(unit => {
            if (unit && unit.form === 'ghost') {
              const unitEl = document.getElementById(unit.id);
              if (unitEl) {
                unitEl.classList.add('ghost-form');
                unitEl.innerHTML = `👻<div class="health-bar"><div class="health-bar-fill" style="width: ${(unit.health / unit.maxHealth) * 100}%"></div></div>`;
              }
            }
          });

          // Only start if player1 still has units
          if (player1Units.some(u => u && (u.health > 0 || u.isRecharging))) {
            battleStarted = true;
            document.getElementById('status').textContent = 'Battle in progress!';
            requestAnimationFrame(gameLoop);
          }
        }, 800);
        return;
      }
    } else {
      // Other modes
      if (!player1Alive || !player2Alive) {
        battleStarted = false;
        gameEnded = true;
        handleGameResult(player1Alive);
        clearBattlefieldEffects();
        return;
      }
    }
    lastUpdate = now;
  }
  requestAnimationFrame(gameLoop);
};

function handleInfiniteResult(playerWon) {
  const resultScreen = document.getElementById('resultScreen');
  const resultTitle = document.getElementById('resultTitle');
  const resultSubtitle = document.getElementById('resultSubtitle');
  const resultReward = document.getElementById('resultReward');
  const continueButton = document.getElementById('continueButton');
  const replayButton = document.getElementById('replayButton');

  resultScreen.style.display = 'flex';
  
  // Track infinite mode result
  trackGameAction('Infinite Mode Ended', {
    result: playerWon ? 'Victory' : 'Defeat',
    wave: (gameState.infiniteWave || 1) - 1
  });
  replayButton.style.display = 'block';
  replayButton.textContent = 'Play Again';
  continueButton.textContent = 'Go Home';

  if (!playerWon) {
    resultTitle.textContent = 'Defeat!';
    const wave = (gameState.infiniteWave || 1) - 1;
    resultSubtitle.textContent = `You reached wave ${wave}!`;
    // Reward: 15 coins per wave reached
    const rewardCoins = Math.max(0, wave * 15);
    if (rewardCoins > 0) {
      resultReward.textContent = `+${rewardCoins} Coins`;
      resultReward.style.display = 'block';
      gameState.coins += rewardCoins;
      saveGameState();
      document.getElementById('coinBalance').textContent = gameState.coins;
      document.getElementById('shopCoinBalance').textContent = gameState.coins;
      document.getElementById('inGameCoinBalance').textContent = gameState.coins;
    } else {
      resultReward.style.display = 'none';
    }
  } else {
    resultTitle.textContent = 'Victory!';
    resultSubtitle.textContent = 'You beat infinite mode! (Impossible?)';
    resultReward.style.display = 'none';
  }
}

// Patch replay/continue for infinite mode
const originalReplay = document.getElementById('replayButton').onclick;
document.getElementById('replayButton').onclick = () => {
  document.getElementById('resultScreen').style.display = 'none';
  if (gameState.currentMode === 'infinite') {
    gameState.infiniteWave = 1;
    resetGame();
    document.getElementById('gameModeInfo').textContent = 'Infinite Mode - Wave 1';
    setTimeout(() => setupInfiniteEnemies(1), 0);
    // Re-enable character selection
    setTimeout(() => {
      const p1Chars = document.getElementById('player1-characters');
      const p2Chars = document.getElementById('player2-characters');
      if (p1Chars) Array.from(p1Chars.children).forEach(el => el.style.pointerEvents = 'auto');
      if (p2Chars) Array.from(p2Chars.children).forEach(el => el.style.pointerEvents = 'auto');
    }, 100);
  } else if (originalReplay) {
    originalReplay();
  }
};

const originalContinue = document.getElementById('continueButton').onclick;
document.getElementById('continueButton').onclick = () => {
  document.getElementById('resultScreen').style.display = 'none';
  if (gameState.currentMode === 'infinite') {
    showScreen('home');
    updateUI();
  } else if (originalContinue) {
    originalContinue();
  }
};

// --- Helper to clear only enemy units and effects for infinite mode ---
function clearEnemyUnitsAndEffects() {
  // Remove only player2 units and all clones/projectiles/effects
  const battlefield = document.getElementById('battlefield');
  // Remove enemy units
  player2Units.forEach(u => {
    if (u && u.id) {
      const el = document.getElementById(u.id);
      if (el) el.remove();
    }
  });
  // Remove all clones
  player1Clones.forEach(u => {
    if (u && u.id) {
      const el = document.getElementById(u.id);
      if (el) el.remove();
    }
  });
  player2Clones.forEach(u => {
    if (u && u.id) {
      const el = document.getElementById(u.id);
      if (el) el.remove();
    }
  });
  // Remove projectiles and effects (but keep ghost-form for player1 units)
  battlefield.querySelectorAll('.projectile, .explosion-effect, .explosion-effect.enhanced, .blood-explosion-effect, .blood-splatter, .drain-effect, .stun-effect, .damage-text, .clone').forEach(el => el.remove());

  // Only remove ghost-form from player2 units (enemies), not player1 units
  player2Units.forEach(u => {
    if (u && u.id) {
      const el = document.getElementById(u.id);
      if (el && el.classList.contains('ghost-form')) {
        el.classList.remove('ghost-form');
      }
    }
  });
}

// Also hide Surrender button when going back to home from game
const originalBackToHome = document.getElementById('backToHomeButton').onclick;
document.getElementById('backToHomeButton').onclick = function() {
  if (originalBackToHome) originalBackToHome();
};

function createEquipScreen() {
  const equipContent = document.getElementById('equipContent');
  equipContent.innerHTML = '';

  // Character section
  const charSection = document.createElement('div');
  charSection.className = 'equip-section-title';
  charSection.innerHTML = '<h3>🎯 Characters & Equipment</h3>';
  equipContent.appendChild(charSection);

  // Show unlocked characters with their equipped items and equip dropdown
  const unlocked = gameState.unlockedCharacters;
  const ownedItems = Object.entries(gameState.ownedItems).filter(([id, qty]) => qty > 0);

  unlocked.forEach(type => {
    const char = characters.find(c => c.type === type);
    if (!char) return;

    const charDiv = document.createElement('div');
    charDiv.className = 'equip-char-block';

    // Character info
    const charInfo = document.createElement('div');
    charInfo.className = 'char-info';
    if (char.type === 'tungtung' && char.icon) {
      charInfo.innerHTML = `
        <div class="char-name"><img src="${char.icon}" alt="tungtung" style="width:32px;height:32px;vertical-align:middle;margin-right:8px;">${char.type.charAt(0).toUpperCase() + char.type.slice(1)}</div>
      `;
    } else {
      charInfo.innerHTML = `
        <div class="char-name">${char.name ? char.name + ' ' : ''}${char.type ? char.type.charAt(0).toUpperCase() + char.type.slice(1) : ''}</div>
      `;
    }

    // Equipped items display (horizontal row of icons)
    const equippedDiv = document.createElement('div');
    equippedDiv.className = 'equipped-items-container';
    const equipped = gameState.equippedItems[type] || [];
    
    if (equipped.length > 0) {
      equipped.forEach((itemId, index) => {
        const item = items.find(i => i.id === itemId);
        if (item) {
          const itemSlot = document.createElement('div');
          itemSlot.className = 'equipped-item-slot';
          
          const iconSpan = document.createElement('span');
          iconSpan.className = 'equipped-icon';
          if (item.isImage && item.name.endsWith('.png')) {
            iconSpan.innerHTML = `<img src="${item.name}" alt="${item.displayName}" style="width:24px;height:24px;">`;
          } else {
            iconSpan.textContent = item.name ? item.name : '⚔️';
          }
          iconSpan.title = `${item.displayName || item.name || ''}`;
          iconSpan.style.cursor = 'pointer';
          iconSpan.onclick = () => {
            showItemDescriptionModal(item.id);
          };
          
          const unequipBtn = document.createElement('button');
          unequipBtn.className = 'unequip-slot-button';
          unequipBtn.textContent = '×';
          unequipBtn.title = 'Unequip';
          unequipBtn.onclick = () => {
            unequipItemSlot(type, index);
          };
          
          itemSlot.appendChild(iconSpan);
          itemSlot.appendChild(unequipBtn);
          equippedDiv.appendChild(itemSlot);
        }
      });
    }
    
    // Add empty slots if less than 3 items equipped
    for (let i = equipped.length; i < 3; i++) {
      const emptySlot = document.createElement('div');
      emptySlot.className = 'equipped-item-slot empty';
      emptySlot.innerHTML = '<span class="equipped-icon">⚔️</span>';
      equippedDiv.appendChild(emptySlot);
    }

    // Equip dropdown (only show if there are owned items)
    const equipRow = document.createElement('div');
    equipRow.style.marginTop = '10px';
    if (ownedItems.length > 0) {
      // Recalculate ownedItems for up-to-date counts
      const currentOwned = Object.entries(gameState.ownedItems).filter(([id, qty]) => qty > 0);
      const select = document.createElement('select');
      select.className = 'equip-select';
      select.style.marginRight = '8px';
      
      // Get currently equipped items for this character
      const charEquipped = gameState.equippedItems[type] || [];
      
      // Filter out already equipped items and create options
      const availableItems = currentOwned.filter(([id, qty]) => {
        const item = items.find(i => i.id === id);
        if (!item) return false;
        // Only allow reaper_only items for reaper
        if (item.restriction === 'reaper_only' && char.type !== 'reaper') return false;
        // Don't show items that are already equipped
        if (charEquipped.includes(id)) return false;
        return true;
      });
      
      select.innerHTML = '<option value="">-- Select Item --</option>' +
        availableItems.map(([id, qty]) => {
          const item = items.find(i => i.id === id);
          // For image items, show displayName instead of filename
          const displayText = item.isImage && item.name.endsWith('.png')
            ? item.displayName || item.name
            : (item.icon ? item.icon + ' ' : '') + (item.displayName || item.name || 'Item');
          return `<option value="${id}">${displayText} (x${qty})</option>`;
        }).join('');
      equipRow.appendChild(select);

      const equipBtn = document.createElement('button');
      equipBtn.className = 'equip-button';
      equipBtn.textContent = 'Equip';
      
      // Disable equip button if character already has 3 items
      if (charEquipped.length >= 3) {
        equipBtn.disabled = true;
        equipBtn.textContent = 'Full (3/3)';
        equipBtn.style.opacity = '0.5';
        equipBtn.style.cursor = 'not-allowed';
      }
      
      equipBtn.onclick = () => {
        if (select.value && charEquipped.length < 3) equipItem(select.value, type);
      };
      equipRow.appendChild(equipBtn);
    } else {
      equipRow.innerHTML = '<span style="color: var(--valorant-accent);">No Item to Equip</span>';
    }

    charDiv.appendChild(charInfo);
    charDiv.appendChild(equippedDiv);
    charDiv.appendChild(equipRow);
    equipContent.appendChild(charDiv);
  });

  // Owned items section (just list, no equip buttons)
  const ownedSection = document.createElement('div');
  ownedSection.className = 'equip-section-title';
  ownedSection.innerHTML = '<h3>📦 Owned Items</h3>';
  equipContent.appendChild(ownedSection);

  // Always recalculate ownedItems for up-to-date counts
  const currentOwned = Object.entries(gameState.ownedItems).filter(([id, qty]) => qty > 0);
  if (currentOwned.length === 0) {
    const noItemsDiv = document.createElement('div');
    noItemsDiv.className = 'no-items';
    noItemsDiv.innerHTML = '<p>No items owned. Visit the Items Shop to purchase items!</p>';
    equipContent.appendChild(noItemsDiv);
  } else {
    currentOwned.forEach(([id, qty]) => {
      const item = items.find(i => i.id === id);
      if (!item) return;
      const itemDiv = document.createElement('div');
      itemDiv.className = 'equip-item-block';
      const itemIconHtml = item.isImage && item.name.endsWith('.png')
        ? `<img src="${item.name}" alt="${item.displayName}" style="width:32px;height:32px;">`
        : (item.icon ? item.icon : (item.name || ''));

      itemDiv.innerHTML = `
        <div class="item-info">
          <span class="item-icon">${itemIconHtml}</span>
          <div class="item-details">
            <div class="item-name"><b>${item.displayName ? item.displayName : item.name}</b></div>
            <div class="item-stats">${item.description}</div>
            <div class="item-quantity" style="color: var(--valorant-accent); font-weight: 600;">Owned: ${qty}</div>
          </div>
        </div>
      `;
      equipContent.appendChild(itemDiv);
    });
  }
}

// Patch equipItem and unequipItem to always refresh the UI after change
function equipItem(itemId, characterType) {
  const item = items.find(i => i.id === itemId);
  if (!item) return;

  // Initialize equipped items array if it doesn't exist
  if (!gameState.equippedItems[characterType]) {
    gameState.equippedItems[characterType] = [];
  }

  // Check if character already has 3 items equipped
  if (gameState.equippedItems[characterType].length >= 3) {
    showMessage('Character already has 3 items equipped!', 'Equipment Full');
    return;
  }

  // Check if item is already equipped
  if (gameState.equippedItems[characterType].includes(itemId)) {
    showMessage('This item is already equipped!', 'Item Already Equipped');
    return;
  }

  // Add item to equipped items array
  gameState.equippedItems[characterType].push(itemId);
  
  // Reduce owned item count
  if (gameState.ownedItems[itemId]) {
    gameState.ownedItems[itemId]--;
    if (gameState.ownedItems[itemId] < 0) gameState.ownedItems[itemId] = 0;
  }
  saveGameState();

  // Show success message
  showMessage(`Equipped ${item.name} ${item.displayName} to ${characters.find(c => c.type === characterType)?.name || characterType}!`, 'Item Equipped');

  // Refresh equip screen
  createEquipScreen();
}

function unequipItemSlot(characterType, slotIndex) {
  const equippedItems = gameState.equippedItems[characterType];
  if (!equippedItems || !equippedItems[slotIndex]) return;

  const itemId = equippedItems[slotIndex];
  const item = items.find(i => i.id === itemId);
  
  if (item) {
    showMessage(`Unequipped ${item.name} ${item.displayName} from ${characters.find(c => c.type === characterType)?.name || characterType}!`, 'Item Unequipped');
    // Return item to owned items
    if (!gameState.ownedItems[itemId]) gameState.ownedItems[itemId] = 0;
    gameState.ownedItems[itemId]++;
  }

  // Remove item from array
  equippedItems.splice(slotIndex, 1);
  
  // If no items left, remove the character entry entirely
  if (equippedItems.length === 0) {
    delete gameState.equippedItems[characterType];
  }
  
  saveGameState();

  // Refresh equip screen
  createEquipScreen();
}

function unequipItem(characterType) {
  const equippedItems = gameState.equippedItems[characterType];
  if (!equippedItems || equippedItems.length === 0) return;

  // Unequip all items
  equippedItems.forEach(itemId => {
    const item = items.find(i => i.id === itemId);
    if (item) {
      // Return item to owned items
      if (!gameState.ownedItems[itemId]) gameState.ownedItems[itemId] = 0;
      gameState.ownedItems[itemId]++;
    }
  });

  showMessage(`Unequipped all items from ${characters.find(c => c.type === characterType)?.name || characterType}!`, 'Items Unequipped');

  delete gameState.equippedItems[characterType];
  saveGameState();

  // Refresh equip screen
  createEquipScreen();
}

// Add showItemDescriptionModal function
function showItemDescriptionModal(itemId) {
  const item = items.find(i => i.id === itemId);
  if (!item) return;

  const itemIconDisplay = item.isImage && item.name.endsWith('.png')
    ? `<img src="${item.name}" alt="${item.displayName}" style="width:24px;height:24px;vertical-align:middle;margin-right:8px;">`
    : (item.icon ? item.icon + ' ' : '');

  showModal(`${itemIconDisplay}${item.displayName || item.name || ''}`, item.description || '');
}